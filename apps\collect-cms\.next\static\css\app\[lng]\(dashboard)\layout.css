/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!./src/layouts/auth/authlayout.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.authlayout_wrapper__qe8u3 {
  display: grid;
  position: relative;
  height: 100vh;
  overflow: hidden;
}
.authlayout_wrapper__qe8u3 main {
  padding: 3.75rem 2rem 2.5rem;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.authlayout_auth__7p0ZV {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.25rem;
  text-align: center;
  height: 100%;
}
.authlayout_auth__header__wxmRM {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.75rem;
}
.authlayout_auth__logo__B4q00 {
  border: 0.0625rem solid #d0d0d0;
  border-radius: 1rem;
  background-color: #ffffff;
}
.authlayout_auth__title__RrV1A {
  display: grid;
  gap: 0.5rem;
}
.authlayout_auth__title__RrV1A h2 {
  color: #2a2a2a;
  font-weight: 600;
  font-size: clamp(2.375rem, 2.375rem + 0vw, 2.375rem);
}
.authlayout_auth__title__RrV1A span {
  color: #898989;
  white-space: nowrap;
}
.authlayout_auth__7p0ZV form {
  display: grid;
  gap: 1.25rem;
  width: 22.5rem;
}
.authlayout_auth__form__Av3Og {
  display: grid;
  gap: 0.75rem;
}
.authlayout_auth__controller__8VnsL {
  display: grid;
  gap: 0.25rem;
}
.authlayout_auth__controller__8VnsL input {
  --input-bg: #ffffff;
  --input-radius: 0.25rem;
  --input-padding-x: 0.75rem;
  --input-padding-y: 0.75rem;
  --input-height: auto;
  border: 0.0625rem solid #e3e3e3;
  box-shadow: none;
  font-weight: 400 !important;
  font-size: clamp(1rem, 1rem + 0vw, 1rem);
}
.authlayout_auth__controller__8VnsL input::placeholder {
  color: #a0a0a0;
}
.authlayout_auth__controller__8VnsL input:hover {
  border-color: #121212;
}
.authlayout_auth__controller__8VnsL input:focus {
  box-shadow: none;
  border-color: #121212;
}
.authlayout_auth__controller__8VnsL svg {
  cursor: default;
}
.authlayout_auth__error__xGtaz {
  text-align: left;
  color: #cf1313;
}
.authlayout_auth__error__xGtaz input {
  border: 0.0625rem solid #cf1313;
}
.authlayout_auth__error__xGtaz input:hover {
  border-color: #cf1313;
}
.authlayout_auth__error__msg__bRASA {
  font-weight: 500;
  font-size: clamp(0.75rem, 0.75rem + 0vw, 0.75rem);
}
.authlayout_auth__7p0ZV .authlayout_remember__RTMwr {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.authlayout_auth__7p0ZV .authlayout_remember__RTMwr * {
  color: #121212;
  font-weight: 500;
  font-size: clamp(0.875rem, 0.875rem + 0vw, 0.875rem);
}
.authlayout_auth__7p0ZV .authlayout_remember__RTMwr input + label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  --checkbox-size: 1.125rem;
  --checkbox-radius: 0.25rem;
  --checkbox-color: #d0d0d0;
  --checkbox-active-color: #121212;
}
.authlayout_auth__7p0ZV .authlayout_remember__RTMwr input + label::after {
  top: -0.09375rem !important;
  left: -0.25rem !important;
  width: 1.625rem !important;
  height: 1.625rem !important;
}
.authlayout_auth__7p0ZV .authlayout_remember__RTMwr input:checked + label::after {
  background-color: #ffffff;
}
.authlayout_auth__7p0ZV .authlayout_remember__RTMwr a:hover {
  text-decoration: underline;
}
.authlayout_auth__7p0ZV button.authlayout_submit__D8siA {
  --button-padding-y: 0.75rem;
  background-color: #121212;
  color: #ffffff;
  border: 0.0625rem solid #121212;
  font-weight: 600;
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
}
.authlayout_auth__7p0ZV button.authlayout_submit__D8siA:hover {
  background-color: #ffffff;
  color: #121212;
}
.authlayout_auth__7p0ZV .authlayout_note__Z9L2f {
  color: #a0a0a0;
  font-size: clamp(0.875rem, 0.875rem + 0vw, 0.875rem);
}
.authlayout_auth__7p0ZV .authlayout_note__Z9L2f a {
  color: #121212;
  font-weight: 500;
}
.authlayout_auth__7p0ZV .authlayout_note__Z9L2f a:hover {
  text-decoration: underline;
}
.authlayout_auth__copyright__lhvVY {
  color: #a0a0a0;
  font-size: clamp(0.75rem, 0.75rem + 0vw, 0.75rem);
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!./src/layouts/admin/adminlayout.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.adminlayout_wrapper__uyyRm {
  display: grid;
  grid-template-columns: 20.4375rem 1fr;
  position: relative;
  min-height: 100vh;
}
.adminlayout_wrapper__uyyRm aside {
  box-shadow: 0px 4px 24px 0px rgba(69, 69, 69, 0.15);
  position: sticky;
  z-index: 1;
  inset: 0;
  background: #ffffff;
  display: grid;
  gap: 1.5rem;
  align-content: flex-start;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_logo__banner__NG9JK {
  background-color: var(--collect-primary-color-80);
  min-height: 4.6875rem;
  display: grid;
  justify-content: center;
  align-items: center;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_logo__banner__NG9JK svg {
  --icon-size: 3.75rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_content__manager__y2rQZ {
  display: grid;
  gap: 2.5rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_link__kD__c {
  margin-left: 1.75rem;
  margin-right: 1.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__1vlHd {
  padding-right: 1.75rem;
  padding-left: 1.75rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__block__uc4EJ {
  display: grid;
  gap: 0.5rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__block__uc4EJ:not(:first-child) {
  padding-top: 1.5rem;
  margin-top: 1.5rem;
  border-top: 1px solid #b8b8b8;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 {
  margin-left: -1.75rem;
  margin-right: -1.75rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 .adminlayout_pin__k7u_H {
  transform: translateY(-50%);
  left: 0.375rem;
  display: flex;
  position: absolute;
  top: 50%;
  color: #b8b8b8;
  cursor: pointer;
  opacity: 0;
  transition: inherit;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 a {
  transition: inherit;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 li {
  transition: 0.2s var(--ease-transition-2);
  position: relative;
  padding: 0.25rem 1.75rem;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 li:hover {
  background: linear-gradient(-90deg, rgba(255, 199, 0, 0) 0%, rgba(255, 199, 0, 0.2) 100%), #fafafa;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 li:hover .adminlayout_pin__k7u_H {
  opacity: 1;
}
.adminlayout_wrapper__uyyRm aside .adminlayout_navigation__routes__WV2B9 .adminlayout_chevron__0nTEq {
  margin-left: auto;
}
.adminlayout_wrapper__uyyRm main {
  background: #f0f0f0;
  height: 100vh;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
}
.adminlayout_wrapper__uyyRm main .adminlayout_header__MnGK2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}
.adminlayout_wrapper__uyyRm main .adminlayout_header__MnGK2 .adminlayout_header_title__qblWV {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2a2a2a;
}
.adminlayout_wrapper__uyyRm main .adminlayout_header__MnGK2 .adminlayout_header_actions__9MtcJ {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.adminlayout_wrapper__uyyRm main .adminlayout_content__ej2Sz {
  padding: 1.5rem 2rem;
  flex: 1 1;
}
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!../../packages/core/dist/components/Icon/icon.module.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.icon_wrapper__my_JR {
  vertical-align: middle;
  display: inline-block;
  --icon-size: var(--size, 16px);
  font-size: var(--icon-size);
  width: var(--icon-size);
  height: var(--icon-size);
  max-width: var(--icon-size);
  flex: 0 0 var(--icon-size);
}
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[9].use[5]!./src/components/UserMenu/usermenu.module.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.usermenu_user_menu__c_KaG {
  position: relative;
}

.usermenu_user_button__QNnn9 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}
.usermenu_user_button__QNnn9:hover {
  background-color: #d0d0d0;
}

.usermenu_user_avatar__8NkQg {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--collect-primary-color-80);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.usermenu_user_avatar_large__3A_tq {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: var(--collect-primary-color-80);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
}

.usermenu_user_name__nA2VW {
  font-weight: 500;
  color: #414141;
}

.usermenu_chevron__IfhT5 {
  transition: transform 0.2s;
  color: #717171;
}
.usermenu_chevron__IfhT5.usermenu_open__PEps4 {
  transform: rotate(180deg);
}

.usermenu_dropdown_menu__KvYOU {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 15rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.usermenu_dropdown_header__KourO {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.usermenu_user_info__2zfwJ {
  display: flex;
  flex-direction: column;
}

.usermenu_user_fullname__E5Vwg {
  font-weight: 600;
  color: #2a2a2a;
}

.usermenu_user_username__qVyhZ {
  font-size: 0.75rem;
  color: #717171;
}

.usermenu_dropdown_divider__eeIVV {
  height: 1px;
  background-color: #d0d0d0;
  margin: 0;
}

.usermenu_dropdown_items__iLtUn {
  padding: 0.5rem;
}

.usermenu_dropdown_item__oM_sb {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  width: 100%;
  text-align: left;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  color: #414141;
}
.usermenu_dropdown_item__oM_sb:hover {
  background-color: #f0f0f0;
}
