"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/LayoutEditor.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutEditor: function() { return /* binding */ LayoutEditor; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useWindowDimensions.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @collective/ui-lib/src/base/Wrapper */ \"(app-pages-browser)/../../packages/ui-lib/src/base/Wrapper.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _ComponentMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../ComponentMenu */ \"(app-pages-browser)/./src/components/Builder/ComponentMenu/ComponentMenu.tsx\");\n/* harmony import */ var _ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ComponentQuickActions */ \"(app-pages-browser)/./src/components/Builder/ComponentQuickActions/ComponentQuickActions.tsx\");\n/* harmony import */ var _Dnd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../Dnd */ \"(app-pages-browser)/./src/components/Builder/Dnd/Board.tsx\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layouteditor.module.scss */ \"(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar LayoutEditor = function(param) {\n    var children = param.children;\n    _s();\n    var _s1 = $RefreshSig$();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var data = context.data, contentType = context.contentType, configuration = context.configuration, setData = context.setData, components = context.components, setEditingIden = context.setEditingIden, normalizedData = context.normalizedData, setChildComponentData = context.setChildComponentData;\n    var _ref = data !== null && data !== void 0 ? data : {}, commonData = _ref.data;\n    var _ref1 = contentType !== null && contentType !== void 0 ? contentType : {}, uidConfig = _ref1.data;\n    var _ref2 = components !== null && components !== void 0 ? components : {}, uiConfig = _ref2.data;\n    var windowDimension = (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions)();\n    // Handle Headline change\n    var globalField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return \"\";\n        var settings = configuration.data.contentType.settings;\n        var mainFieldKey = settings.mainField;\n        return mainFieldKey;\n    }, [\n        contentType,\n        configuration\n    ]);\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(commonData && globalField ? commonData[globalField] : \"\"), 2), headline = _useState[0], setHeadline = _useState[1];\n    var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleChange = function(event) {\n        if (!textareaRef.current) return;\n        var target = event.target;\n        setHeadline(target.value);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__._)({}, globalField, target.value))\n        }));\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (!textareaRef.current) return;\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    }, [\n        textareaRef,\n        windowDimension\n    ]);\n    // Handle component menu\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), 2), menu = _useState1[0], setMenu = _useState1[1];\n    var triggerMenu = function(e) {\n        var container = e.currentTarget;\n        setMenu(function(prev) {\n            return prev !== container ? container : null;\n        });\n    };\n    var handleAddBlock = function(component) {\n        if (!component) return setMenu(null);\n        var id = Number(menu === null || menu === void 0 ? void 0 : menu.dataset.id);\n        var defaultData = uiConfig.find(function(item) {\n            return item.uid === component.uid;\n        });\n        var attributes = defaultData === null || defaultData === void 0 ? void 0 : defaultData.schema.attributes;\n        if (!attributes) return setMenu(null);\n        var remapProps = Object.entries(attributes).reduce(function(acc, param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n            var newValue;\n            switch(value.type){\n                case \"boolean\":\n                    var _value_default;\n                    newValue = (_value_default = value[\"default\"]) !== null && _value_default !== void 0 ? _value_default : false;\n                    break;\n                case \"string\":\n                    newValue = \"\";\n                    break;\n                default:\n                    newValue = null;\n                    break;\n            }\n            acc[key] = newValue;\n            return acc;\n        }, {});\n        var addData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n            __component: component.uid\n        }, remapProps), {\n            __temp_key__: normalizedData.components.length + 1\n        });\n        var components = normalizedData.components;\n        var index = components.findIndex(function(component) {\n            return component.__temp_key__ === id;\n        });\n        components.splice(index + 1, 0, addData);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), {\n                components: components\n            })\n        }));\n        setMenu(null);\n    };\n    // Get list available components\n    var Modules = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!uidConfig) return {};\n        if (!uidConfig.schema.attributes.components) return {};\n        if (\"components\" in uidConfig.schema.attributes.components === false) return {};\n        var components = {};\n        var arrComponents = uidConfig.schema.attributes.components.components;\n        arrComponents === null || arrComponents === void 0 ? void 0 : arrComponents.forEach(function(module) {\n            var Component = (0,_collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__.CmsWrapper)({\n                module: module\n            });\n            if (Component && components) components[module] = Component;\n        });\n        return components;\n    }, [\n        uidConfig\n    ]);\n    // Component wrapper with hover state\n    var ComponentWrapper = function(param) {\n        var column = param.column, index = param.index;\n        _s1();\n        var Module = (column === null || column === void 0 ? void 0 : column.__component) && Modules && Modules[column.__component];\n        var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isHovered = _useState[0], setIsHovered = _useState[1];\n        var handleEdit = function() {\n            if (!column) return;\n            var id = column.id || column.__temp_key__;\n            setEditingIden({\n                key: column.__component,\n                id: id\n            });\n        // setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            tabIndex: 0,\n            role: \"button\",\n            className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__block),\n            onClick: handleEdit,\n            onKeyDown: handleEdit,\n            onMouseEnter: function() {\n                return setIsHovered(true);\n            },\n            onMouseLeave: function() {\n                return setIsHovered(false);\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__.ComponentQuickActions, {\n                    index: index,\n                    id: column === null || column === void 0 ? void 0 : column.__temp_key__,\n                    isVisible: isHovered\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 5\n                }, _this),\n                Module ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Module, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, column), void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Component \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                children: column === null || column === void 0 ? void 0 : column.__component\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 18\n                            }, _this),\n                            \" failed to import/load\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 7\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, column === null || column === void 0 ? void 0 : column.__temp_key__, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 158,\n            columnNumber: 4\n        }, _this);\n    };\n    _s1(ComponentWrapper, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n    // Column component for Board\n    var ColumnComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComponentWrapper, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, props), void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 189,\n            columnNumber: 11\n        }, _this);\n    }, // ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        Modules,\n        setEditingIden\n    ]);\n    var ColumnAddBlock = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(param) {\n        var column = param.column;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            \"data-id\": column === null || column === void 0 ? void 0 : column.__temp_key__,\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n            onClick: triggerMenu,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                    variant: \"plus-circle\",\n                    type: \"cms\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().line)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 198,\n            columnNumber: 4\n        }, _this);\n    }, []);\n    // Toggle active when trigger menu\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        var allBlockBtn = document.querySelectorAll(\".add__block\");\n        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n        try {\n            for(var _iterator = allBlockBtn[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                var button = _step.value;\n                button.classList.toggle(\"active\", menu === button);\n            }\n        } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n        } finally{\n            try {\n                if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                    _iterator[\"return\"]();\n                }\n            } finally{\n                if (_didIteratorError) {\n                    throw _iteratorError;\n                }\n            }\n        }\n    }, [\n        menu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__image)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"image\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__body--lg\",\n                                children: \"Add cover image\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        rows: 1,\n                        placeholder: \"Post Title\",\n                        ref: textareaRef,\n                        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().headline),\n                        value: headline,\n                        onChange: handleChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        \"data-id\": \"0\",\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n                        onClick: triggerMenu,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"plus-circle\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__heading--h6\",\n                                children: \"Add block\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 219,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Dnd__WEBPACK_IMPORTED_MODULE_14__.Board, {\n                initial: normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.components,\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnComponent, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnAddBlock, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 242,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentMenu__WEBPACK_IMPORTED_MODULE_15__.ComponentMenu, {\n                trigger: menu,\n                onClose: handleAddBlock\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 247,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n        lineNumber: 218,\n        columnNumber: 3\n    }, _this);\n};\n_s(LayoutEditor, \"Lwv10dbcrNmFEGn3WObpwHKLwJY=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayoutEditor;\nvar _c;\n$RefreshReg$(_c, \"LayoutEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx\n"));

/***/ })

});