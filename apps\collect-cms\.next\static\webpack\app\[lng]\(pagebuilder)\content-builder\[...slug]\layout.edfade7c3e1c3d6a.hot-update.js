/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss":
/*!**********************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/layouteditor.module.scss ***!
  \**********************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"layouteditor_wrapper__DTQyS\",\"headline\":\"layouteditor_headline__O4_34\",\"header\":\"layouteditor_header__z3D_M\",\"add__image\":\"layouteditor_add__image__xBFIf\",\"add__block\":\"layouteditor_add__block__1QfXW\",\"body\":\"layouteditor_body__W9sTh\",\"line\":\"layouteditor_line__OYy4W\",\"component__block\":\"layouteditor_component__block__0NfPQ\",\"component__block__error\":\"layouteditor_component__block__error__lrOqP\"};\n    if(true) {\n      // 1748423862309\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"5d319e4e9fb2\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0J1aWxkZXIvTGF5b3V0RWRpdG9yL2xheW91dGVkaXRvci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQjtBQUNsQixPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBbUosY0FBYyxzREFBc0Q7QUFDclAsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9CdWlsZGVyL0xheW91dEVkaXRvci9sYXlvdXRlZGl0b3IubW9kdWxlLnNjc3M/NzdmMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wid3JhcHBlclwiOlwibGF5b3V0ZWRpdG9yX3dyYXBwZXJfX0RUUXlTXCIsXCJoZWFkbGluZVwiOlwibGF5b3V0ZWRpdG9yX2hlYWRsaW5lX19PNF8zNFwiLFwiaGVhZGVyXCI6XCJsYXlvdXRlZGl0b3JfaGVhZGVyX196M0RfTVwiLFwiYWRkX19pbWFnZVwiOlwibGF5b3V0ZWRpdG9yX2FkZF9faW1hZ2VfX3hCRklmXCIsXCJhZGRfX2Jsb2NrXCI6XCJsYXlvdXRlZGl0b3JfYWRkX19ibG9ja19fMVFmWFdcIixcImJvZHlcIjpcImxheW91dGVkaXRvcl9ib2R5X19XOXNUaFwiLFwibGluZVwiOlwibGF5b3V0ZWRpdG9yX2xpbmVfX09ZeTRXXCIsXCJjb21wb25lbnRfX2Jsb2NrXCI6XCJsYXlvdXRlZGl0b3JfY29tcG9uZW50X19ibG9ja19fME5mUFFcIixcImNvbXBvbmVudF9fYmxvY2tfX2Vycm9yXCI6XCJsYXlvdXRlZGl0b3JfY29tcG9uZW50X19ibG9ja19fZXJyb3JfX2xyT3FQXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NDg0MjM4NjIzMDlcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRDovQ0RBL3JlcG9zL2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUvYXBwcy9jb2xsZWN0LWNtcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI1ZDMxOWU0ZTlmYjJcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\n"));

/***/ })

});