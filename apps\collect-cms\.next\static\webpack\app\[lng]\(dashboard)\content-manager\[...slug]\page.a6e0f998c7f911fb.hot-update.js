"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Selection: function() { return /* binding */ Selection; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _selection_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./selection.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/selection.module.scss\");\n/* harmony import */ var _selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_selection_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nvar Selection = function(props) {\n    _s();\n    var required = props.required, value = props.value, onChange = props.onChange, name = props.name, placeholder = props.placeholder, enumOptions = props[\"enum\"], options = props.options;\n    var selectionMode = Array.isArray(value) ? \"multiple\" : \"single\";\n    // console.log(value)\n    var reformatedValue = function() {\n        // Handle null or undefined value\n        if (value === null || value === undefined) {\n            // For multiple selection, return empty array\n            if (selectionMode === \"multiple\") {\n                return [];\n            }\n            // For single selection, return undefined to use placeholder\n            return undefined;\n        }\n        // Handle array values\n        if (Array.isArray(value)) {\n            return value.map(function(v) {\n                return {\n                    label: v,\n                    value: v\n                };\n            });\n        }\n        // Handle single value\n        return {\n            label: value,\n            value: value\n        };\n    };\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(reformatedValue), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    // console.log(props)\n    (0,_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)(function() {\n        setPropsValue(reformatedValue);\n    }, [\n        value\n    ]);\n    // Determine if the component should be disabled (when value is null)\n    var isDisabled = value === null;\n    console.log(props[\"enum\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper), isDisabled && (_selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default().disabled)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.Select, {\n            className: \"collect__input has__border\",\n            required: required,\n            mode: selectionMode,\n            endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                type: \"cms\",\n                variant: \"chevron-down\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                lineNumber: 61,\n                columnNumber: 14\n            }, void 0),\n            placeholder: isDisabled ? \"Not available\" : placeholder,\n            defaultOption: propsValue,\n            onChange: function(e) {\n                // If disabled, don't process changes\n                if (isDisabled) return;\n                var values = Array.isArray(e) ? e.map(function(item) {\n                    return item.value;\n                }) : e === null || e === void 0 ? void 0 : e.value;\n                onChange === null || onChange === void 0 ? void 0 : onChange({\n                    field: name,\n                    value: selectionMode === \"multiple\" ? values : values\n                });\n            },\n            children: isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                value: \"Not available\",\n                children: \"Not available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                lineNumber: 77,\n                columnNumber: 6\n            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (enumOptions || options).map(function(option, idx) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                        value: option,\n                        children: option\n                    }, idx, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 8\n                    }, _this);\n                })\n            }, void 0, false)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n            lineNumber: 57,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, _this);\n};\n_s(Selection, \"QbBeCgdSr7y1WxEUQqhuk7n4XlA=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Selection;\nvar _c;\n$RefreshReg$(_c, \"Selection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx\n"));

/***/ })

});