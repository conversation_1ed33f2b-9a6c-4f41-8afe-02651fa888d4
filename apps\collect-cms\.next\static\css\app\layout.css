/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[13].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[13].use[5]!./src/styles/globals.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --collect-primary-color-80: #ffc700;
  --ease-transition: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-transition-2: cubic-bezier(0.22, 1, 0.36, 1);
  --toolbar-height: 3.75rem;
}

html,
body {
  color: #2a2a2a;
  font-size: clamp(1rem, 1rem + 0vw, 1rem);
  line-height: 1.4;
}

.collect__grid {
  display: grid;
  gap: 1rem;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}
@media (min-width: 75rem) {
  .collect__grid {
    column-gap: 1.25rem;
  }
}
@media (min-width: 48rem) {
  .collect__grid.grid__layout {
    grid-template-columns: repeat(6, 1fr);
  }
}
@media (min-width: 75rem) {
  .collect__grid.grid__layout {
    grid-template-columns: repeat(12, 1fr);
  }
}

[class^=collect__heading] {
  font-weight: 600;
  line-height: normal;
}

h1.collect__heading,
.collect__heading--h1 {
  font-size: clamp(3rem, 3rem + 0vw, 3rem);
}

h2.collect__heading,
.collect__heading--h2 {
  font-size: clamp(2.375rem, 2.375rem + 0vw, 2.375rem);
}

h3.collect__heading,
.collect__heading--h3 {
  font-size: clamp(2rem, 2rem + 0vw, 2rem);
}

h4.collect__heading,
.collect__heading--h4 {
  font-size: clamp(1.75rem, 1.75rem + 0vw, 1.75rem);
}

h5.collect__heading,
.collect__heading--h5 {
  font-size: clamp(1.5rem, 1.5rem + 0vw, 1.5rem);
}

h6.collect__heading,
.collect__heading--h6 {
  font-size: clamp(1.25rem, 1.25rem + 0vw, 1.25rem);
}

[class^=collect__body] {
  line-height: 1.4;
}
[class^=collect__body].semi__bold {
  font-weight: 600;
}

.collect__body--md {
  font-size: clamp(1rem, 1rem + 0vw, 1rem);
}

.collect__body--lg {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
}

.collect__body--sm {
  font-size: clamp(0.875rem, 0.875rem + 0vw, 0.875rem);
}

.collect__body--xs {
  font-size: clamp(0.75rem, 0.75rem + 0vw, 0.75rem);
}

[class^=collect__label] {
  font-weight: 600;
}

.collect__label {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  line-height: 1.3;
}

.collect__label--uppercase {
  font-size: clamp(1rem, 1rem + 0vw, 1rem);
  text-transform: uppercase;
}

button.collect__button {
  padding: 0.5rem 1.5rem;
  background-color: #ffffff;
  color: #5a5a5a;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 1.5rem rgba(69, 69, 69, 0.15);
}
button.collect__button span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
button.collect__button--sm {
  font-size: clamp(0.75rem, 0.75rem + 0vw, 0.75rem);
  font-weight: 600;
}
button.collect__button--md {
  font-size: clamp(0.75rem, 0.75rem + 0vw, 0.75rem);
  font-weight: 600;
}
button.collect__button--lg {
  font-size: clamp(0.875rem, 0.875rem + 0vw, 0.875rem);
  font-weight: 600;
}
button.collect__button.yellow {
  background-color: #ffc700;
  color: #2a2a2a;
}
button.collect__button.black {
  background-color: #121212;
  color: #ffffff;
}

.collect__buttons {
  display: grid;
  gap: 0.75rem;
}

.collect__subheader {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  font-weight: 600;
  line-height: 1.3;
  text-transform: capitalize;
}
.collect__subheader span {
  font-weight: 600;
  margin-left: 0.125rem;
}

.text-w-icon {
  --icon-gap: 0.5rem;
  display: flex;
  align-items: center;
  gap: var(--icon-gap);
}

.content__manager {
  display: grid;
  gap: 2.5rem;
}

.page__header {
  display: grid;
  gap: 1.5rem;
}
.page__header .text-w-icon {
  cursor: pointer;
  --icon-gap: 0;
}

.page__headline h1 {
  display: inline;
}
.page__headline a {
  vertical-align: 25%;
  margin-left: 0.625rem;
}
.page__headline svg {
  --icon-size: 1.5rem;
}

.flex__center {
  align-items: center;
  justify-content: center;
  display: flex;
}

.text__w--icon {
  --icon-gap: 0.75rem;
  align-items: center;
  justify-content: center;
  display: flex;
  gap: var(--icon-gap);
}
.text__w--icon.align__center {
  align-items: center;
}

div.collect__input {
  --input-height: 2.5625rem;
  --input-border-color: transparent;
  --input-radius: 0.5rem;
  --input-bg: #ffffff;
  --input-padding-x: 1rem;
  --input-padding-y: 0.5rem;
  width: 100%;
}
div.collect__input.clickable {
  cursor: pointer;
}
div.collect__input-group {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}
div.collect__input-group.stacked {
  flex-direction: column;
}
div.collect__input.has__border {
  --input-border-color: #e3e3e3;
}
div.collect__input::placeholder {
  color: #a0a0a0;
}
div.collect__input span {
  pointer-events: auto !important;
}
div.collect__input span svg,
div.collect__input svg {
  cursor: pointer;
  --size: 1rem;
  color: #898989;
}
div.collect__input input {
  font-weight: 400;
}
div.collect__input input::placeholder {
  color: #b8b8b8;
  font-weight: 400;
}
div.collect__input input::-webkit-datetime-edit {
  display: block;
  width: 100%;
}
div.collect__input input::-webkit-inner-spin-button, div.collect__input input::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}

div.select__dropdown {
  --dropdown-bg-color: #ffffff;
  z-index: 11;
}

.ck.ck-editor__editable_inline {
  min-height: 7.5rem;
}
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[13].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[13].use[5]!../../packages/ui-lib/src/styles/global.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
*,
*::after,
*::before {
  box-sizing: border-box;
}

* {
  font: inherit;
  margin: 0;
  padding: 0;
  border: 0;
}

ol,
ul,
menu {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

mark {
  background-color: transparent;
  color: inherit;
}

button,
input,
textarea,
select,
.reset {
  border: 0;
  background-color: transparent;
  border-radius: 0;
  color: inherit;
  line-height: inherit;
  appearance: none;
}

textarea {
  resize: vertical;
  overflow: auto;
  vertical-align: top;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

img,
video,
svg {
  max-width: 100%;
  display: block;
}

b,
strong {
  font-weight: bold;
}

:root {
  --ease-transition: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-transition-2: cubic-bezier(0.22, 1, 0.36, 1);
}

.aidigi__display {
  font-size: clamp(3rem, 1.2142857143rem + 8.9285714286vw, 5.5rem);
  line-height: 1.2;
  font-family: var(--anton-font);
}

h1.aidigi__heading,
.aidigi__heading--h1 {
  font-size: clamp(2.25rem, 1rem + 6.25vw, 4rem);
  line-height: 1.2;
  font-family: var(--anton-font);
}

h2.aidigi__heading,
.aidigi__heading--h2 {
  font-size: clamp(1.875rem, 1.7857142857rem + 0.4464285714vw, 2rem);
  line-height: 1.2;
  font-family: var(--anton-font);
  font-weight: 600;
}

h3.aidigi__heading,
.aidigi__heading--h3 {
  font-size: clamp(1.625rem, 1.7142857143rem + -0.4464285714vw, 1.5rem);
  line-height: 1.2;
  font-family: var(--anton-font);
  font-weight: 600;
}

h4.aidigi__heading,
.aidigi__heading--h4 {
  font-size: clamp(1.5rem, 1.3214285714rem + 0.8928571429vw, 1.75rem);
  line-height: 1.2;
}

h5.aidigi__heading,
.aidigi__heading--h5 {
  font-size: clamp(1.25rem, 1.0714285714rem + 0.8928571429vw, 1.5rem);
  line-height: 1.2;
}

h6.aidigi__heading,
.aidigi__heading--h6 {
  font-size: clamp(1rem, 0.9107142857rem + 0.4464285714vw, 1.125rem);
  line-height: 1.3;
  font-weight: 600;
}

.aidigi__label {
  font-size: clamp(0.875rem, 0.7857142857rem + 0.4464285714vw, 1rem);
  line-height: 1.2;
  font-weight: 600;
  text-transform: uppercase;
}

p.aidigi__paragraph,
.aidigi__paragraph--md {
  font-size: clamp(0.9375rem, 0.9375rem + 0vw, 0.9375rem);
  line-height: 1.5;
  font-weight: 500;
}

p.aidigi__paragraph,
.aidigi__paragraph--lg {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  line-height: 1.5;
  font-weight: 500;
}

.aidigi__button,
.aidigi__link {
  font-size: clamp(1.125rem, 1.125rem + 0vw, 1.125rem);
  line-height: 1.4;
  font-weight: 700;
}
.aidigi__button.outline,
.aidigi__link.outline {
  --icon-gap: 0.625rem;
  border-radius: 0.625rem;
  border: 1px solid #1d1d1f;
  padding: 1rem 1.5rem;
}
.aidigi__button:hover,
.aidigi__link:hover {
  background-color: #1d1d1f;
  color: #fafafa;
}
.aidigi__button.disabled,
.aidigi__link.disabled {
  color: #a8a8a8;
  border-color: #a8a8a8;
}
.aidigi__button.red,
.aidigi__link.red {
  color: #fafafa;
  background-color: #ee1d52;
  border: 0.0625rem solid #ee1d52;
}
.aidigi__button.red:hover,
.aidigi__link.red:hover {
  color: #ee1d52;
  background-color: #fafafa;
}

a {
  color: inherit;
  text-decoration: none;
}

section.first__section {
  margin-top: 0;
}
section.first__section::before {
  display: none;
}

.page__content section .aidigi__grid {
  padding-left: 0;
  padding-right: 0;
}
