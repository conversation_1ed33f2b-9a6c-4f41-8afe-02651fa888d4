"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/LayoutEditor.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutEditor: function() { return /* binding */ LayoutEditor; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useWindowDimensions.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @collective/ui-lib/src/base/Wrapper */ \"(app-pages-browser)/../../packages/ui-lib/src/base/Wrapper.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _ComponentMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../ComponentMenu */ \"(app-pages-browser)/./src/components/Builder/ComponentMenu/ComponentMenu.tsx\");\n/* harmony import */ var _ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ComponentQuickActions */ \"(app-pages-browser)/./src/components/Builder/ComponentQuickActions/ComponentQuickActions.tsx\");\n/* harmony import */ var _Dnd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../Dnd */ \"(app-pages-browser)/./src/components/Builder/Dnd/Board.tsx\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layouteditor.module.scss */ \"(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar LayoutEditor = function(param) {\n    var children = param.children;\n    _s();\n    var _s1 = $RefreshSig$();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var data = context.data, contentType = context.contentType, configuration = context.configuration, setData = context.setData, components = context.components, setEditingIden = context.setEditingIden, normalizedData = context.normalizedData, setChildComponentData = context.setChildComponentData;\n    var _ref = data !== null && data !== void 0 ? data : {}, commonData = _ref.data;\n    var _ref1 = contentType !== null && contentType !== void 0 ? contentType : {}, uidConfig = _ref1.data;\n    var _ref2 = components !== null && components !== void 0 ? components : {}, uiConfig = _ref2.data;\n    var windowDimension = (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions)();\n    // Handle Headline change\n    var globalField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return \"\";\n        var settings = configuration.data.contentType.settings;\n        var mainFieldKey = settings.mainField;\n        return mainFieldKey;\n    }, [\n        contentType,\n        configuration\n    ]);\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(commonData && globalField ? commonData[globalField] : \"\"), 2), headline = _useState[0], setHeadline = _useState[1];\n    var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleChange = function(event) {\n        if (!textareaRef.current) return;\n        var target = event.target;\n        setHeadline(target.value);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__._)({}, globalField, target.value))\n        }));\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (!textareaRef.current) return;\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    }, [\n        textareaRef,\n        windowDimension\n    ]);\n    // Handle component menu\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), 2), menu = _useState1[0], setMenu = _useState1[1];\n    var triggerMenu = function(e) {\n        var container = e.currentTarget;\n        setMenu(function(prev) {\n            return prev !== container ? container : null;\n        });\n    };\n    var handleAddBlock = function(component) {\n        if (!component) return setMenu(null);\n        var id = Number(menu === null || menu === void 0 ? void 0 : menu.dataset.id);\n        var defaultData = uiConfig.find(function(item) {\n            return item.uid === component.uid;\n        });\n        var attributes = defaultData === null || defaultData === void 0 ? void 0 : defaultData.schema.attributes;\n        if (!attributes) return setMenu(null);\n        var remapProps = Object.entries(attributes).reduce(function(acc, param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n            var newValue;\n            switch(value.type){\n                case \"boolean\":\n                    var _value_default;\n                    newValue = (_value_default = value[\"default\"]) !== null && _value_default !== void 0 ? _value_default : false;\n                    break;\n                case \"string\":\n                    newValue = \"\";\n                    break;\n                default:\n                    newValue = null;\n                    break;\n            }\n            acc[key] = newValue;\n            return acc;\n        }, {});\n        var addData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n            __component: component.uid\n        }, remapProps), {\n            __temp_key__: normalizedData.components.length + 1\n        });\n        var components = normalizedData.components;\n        var index = components.findIndex(function(component) {\n            return component.__temp_key__ === id;\n        });\n        components.splice(index + 1, 0, addData);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), {\n                components: components\n            })\n        }));\n        setMenu(null);\n    };\n    // Get list available components\n    var Modules = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!uidConfig) return {};\n        if (!uidConfig.schema.attributes.components) return {};\n        if (\"components\" in uidConfig.schema.attributes.components === false) return {};\n        var components = {};\n        var arrComponents = uidConfig.schema.attributes.components.components;\n        arrComponents === null || arrComponents === void 0 ? void 0 : arrComponents.forEach(function(module) {\n            var Component = (0,_collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__.CmsWrapper)({\n                module: module\n            });\n            if (Component && components) components[module] = Component;\n        });\n        return components;\n    }, [\n        uidConfig\n    ]);\n    // Component wrapper with hover state\n    var ComponentWrapper = function(param) {\n        var column = param.column, index = param.index;\n        _s1();\n        var Module = (column === null || column === void 0 ? void 0 : column.__component) && Modules && Modules[column.__component];\n        var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isHovered = _useState[0], setIsHovered = _useState[1];\n        var handleEdit = function() {\n            if (!column) return;\n            var id = column.id || column.__temp_key__;\n            setEditingIden({\n                key: column.__component,\n                id: id\n            });\n        // setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            tabIndex: 0,\n            role: \"button\",\n            className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__block),\n            onClick: handleEdit,\n            onKeyDown: handleEdit,\n            onMouseEnter: function() {\n                return setIsHovered(true);\n            },\n            onMouseLeave: function() {\n                return setIsHovered(false);\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__.ComponentQuickActions, {\n                    index: index,\n                    id: column === null || column === void 0 ? void 0 : column.__temp_key__,\n                    isVisible: isHovered\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 5\n                }, _this),\n                Module ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Module, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, column), void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__error)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, column === null || column === void 0 ? void 0 : column.__temp_key__, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 158,\n            columnNumber: 4\n        }, _this);\n    };\n    _s1(ComponentWrapper, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n    // Column component for Board\n    var ColumnComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComponentWrapper, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, props), void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 189,\n            columnNumber: 11\n        }, _this);\n    }, // ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        Modules,\n        setEditingIden\n    ]);\n    var ColumnAddBlock = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(param) {\n        var column = param.column;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            \"data-id\": column === null || column === void 0 ? void 0 : column.__temp_key__,\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n            onClick: triggerMenu,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                    variant: \"plus-circle\",\n                    type: \"cms\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().line)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 198,\n            columnNumber: 4\n        }, _this);\n    }, []);\n    // Toggle active when trigger menu\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        var allBlockBtn = document.querySelectorAll(\".add__block\");\n        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n        try {\n            for(var _iterator = allBlockBtn[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                var button = _step.value;\n                button.classList.toggle(\"active\", menu === button);\n            }\n        } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n        } finally{\n            try {\n                if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                    _iterator[\"return\"]();\n                }\n            } finally{\n                if (_didIteratorError) {\n                    throw _iteratorError;\n                }\n            }\n        }\n    }, [\n        menu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__image)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"image\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__body--lg\",\n                                children: \"Add cover image\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        rows: 1,\n                        placeholder: \"Post Title\",\n                        ref: textareaRef,\n                        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().headline),\n                        value: headline,\n                        onChange: handleChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        \"data-id\": \"0\",\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n                        onClick: triggerMenu,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"plus-circle\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__heading--h6\",\n                                children: \"Add block\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 219,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Dnd__WEBPACK_IMPORTED_MODULE_14__.Board, {\n                initial: normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.components,\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnComponent, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnAddBlock, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 242,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentMenu__WEBPACK_IMPORTED_MODULE_15__.ComponentMenu, {\n                trigger: menu,\n                onClose: handleAddBlock\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 247,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n        lineNumber: 218,\n        columnNumber: 3\n    }, _this);\n};\n_s(LayoutEditor, \"Lwv10dbcrNmFEGn3WObpwHKLwJY=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayoutEditor;\nvar _c;\n$RefreshReg$(_c, \"LayoutEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx\n"));

/***/ })

});