"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/LayoutEditor.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutEditor: function() { return /* binding */ LayoutEditor; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useWindowDimensions.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @collective/ui-lib/src/base/Wrapper */ \"(app-pages-browser)/../../packages/ui-lib/src/base/Wrapper.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _ComponentMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../ComponentMenu */ \"(app-pages-browser)/./src/components/Builder/ComponentMenu/ComponentMenu.tsx\");\n/* harmony import */ var _ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ComponentQuickActions */ \"(app-pages-browser)/./src/components/Builder/ComponentQuickActions/ComponentQuickActions.tsx\");\n/* harmony import */ var _Dnd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../Dnd */ \"(app-pages-browser)/./src/components/Builder/Dnd/Board.tsx\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layouteditor.module.scss */ \"(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar LayoutEditor = function(param) {\n    var children = param.children;\n    _s();\n    var _s1 = $RefreshSig$();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var data = context.data, contentType = context.contentType, configuration = context.configuration, setData = context.setData, components = context.components, setEditingIden = context.setEditingIden, normalizedData = context.normalizedData;\n    var _ref = data !== null && data !== void 0 ? data : {}, commonData = _ref.data;\n    var _ref1 = contentType !== null && contentType !== void 0 ? contentType : {}, uidConfig = _ref1.data;\n    var _ref2 = components !== null && components !== void 0 ? components : {}, uiConfig = _ref2.data;\n    var windowDimension = (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions)();\n    // Handle Headline change\n    var globalField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return \"\";\n        var settings = configuration.data.contentType.settings;\n        var mainFieldKey = settings.mainField;\n        return mainFieldKey;\n    }, [\n        contentType,\n        configuration\n    ]);\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(commonData && globalField ? commonData[globalField] : \"\"), 2), headline = _useState[0], setHeadline = _useState[1];\n    var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleChange = function(event) {\n        if (!textareaRef.current) return;\n        var target = event.target;\n        setHeadline(target.value);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__._)({}, globalField, target.value))\n        }));\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (!textareaRef.current) return;\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    }, [\n        textareaRef,\n        windowDimension\n    ]);\n    // Handle component menu\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), 2), menu = _useState1[0], setMenu = _useState1[1];\n    var triggerMenu = function(e) {\n        var container = e.currentTarget;\n        setMenu(function(prev) {\n            return prev !== container ? container : null;\n        });\n    };\n    var handleAddBlock = function(component) {\n        if (!component) return setMenu(null);\n        var id = Number(menu === null || menu === void 0 ? void 0 : menu.dataset.id);\n        var defaultData = uiConfig.find(function(item) {\n            return item.uid === component.uid;\n        });\n        var attributes = defaultData === null || defaultData === void 0 ? void 0 : defaultData.schema.attributes;\n        if (!attributes) return setMenu(null);\n        var remapProps = Object.entries(attributes).reduce(function(acc, param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n            var newValue;\n            switch(value.type){\n                case \"boolean\":\n                    var _value_default;\n                    newValue = (_value_default = value[\"default\"]) !== null && _value_default !== void 0 ? _value_default : false;\n                    break;\n                case \"string\":\n                    newValue = \"\";\n                    break;\n                default:\n                    newValue = null;\n                    break;\n            }\n            acc[key] = newValue;\n            return acc;\n        }, {});\n        var addData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n            __component: component.uid\n        }, remapProps), {\n            __temp_key__: normalizedData.components.length + 1\n        });\n        var components = normalizedData.components;\n        var index = components.findIndex(function(component) {\n            return component.__temp_key__ === id;\n        });\n        components.splice(index + 1, 0, addData);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), {\n                components: components\n            })\n        }));\n        setMenu(null);\n    };\n    // Get list available components\n    var Modules = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!uidConfig) return {};\n        if (!uidConfig.schema.attributes.components) return {};\n        if (\"components\" in uidConfig.schema.attributes.components === false) return {};\n        var components = {};\n        var arrComponents = uidConfig.schema.attributes.components.components;\n        arrComponents === null || arrComponents === void 0 ? void 0 : arrComponents.forEach(function(module) {\n            var Component = (0,_collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__.CmsWrapper)({\n                module: module\n            });\n            if (Component && components) components[module] = Component;\n        });\n        return components;\n    }, [\n        uidConfig\n    ]);\n    // Component wrapper with hover state\n    var ComponentWrapper = function(param) {\n        var column = param.column, index = param.index;\n        _s1();\n        var Module = (column === null || column === void 0 ? void 0 : column.__component) && Modules && Modules[column.__component];\n        var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isHovered = _useState[0], setIsHovered = _useState[1];\n        var handleEdit = function() {\n            if (!column) return;\n            var id = column.id || column.__temp_key__;\n            setEditingIden({\n                key: column.__component,\n                id: id\n            });\n        // setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })\n        };\n        console.log(Modules);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            tabIndex: 0,\n            role: \"button\",\n            className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__block),\n            onClick: handleEdit,\n            onKeyDown: handleEdit,\n            onMouseEnter: function() {\n                return setIsHovered(true);\n            },\n            onMouseLeave: function() {\n                return setIsHovered(false);\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__.ComponentQuickActions, {\n                    index: index,\n                    id: column === null || column === void 0 ? void 0 : column.__temp_key__,\n                    isVisible: isHovered\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 5\n                }, _this),\n                Module ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Module, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, column), void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__error)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, column === null || column === void 0 ? void 0 : column.__temp_key__, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 158,\n            columnNumber: 4\n        }, _this);\n    };\n    _s1(ComponentWrapper, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n    // Column component for Board\n    var ColumnComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComponentWrapper, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, props), void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 189,\n            columnNumber: 11\n        }, _this);\n    }, // ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        Modules,\n        setEditingIden\n    ]);\n    var ColumnAddBlock = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(param) {\n        var column = param.column;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            \"data-id\": column === null || column === void 0 ? void 0 : column.__temp_key__,\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n            onClick: triggerMenu,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                    variant: \"plus-circle\",\n                    type: \"cms\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().line)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 198,\n            columnNumber: 4\n        }, _this);\n    }, []);\n    // Toggle active when trigger menu\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        var allBlockBtn = document.querySelectorAll(\".add__block\");\n        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n        try {\n            for(var _iterator = allBlockBtn[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                var button = _step.value;\n                button.classList.toggle(\"active\", menu === button);\n            }\n        } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n        } finally{\n            try {\n                if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                    _iterator[\"return\"]();\n                }\n            } finally{\n                if (_didIteratorError) {\n                    throw _iteratorError;\n                }\n            }\n        }\n    }, [\n        menu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__image)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"image\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__body--lg\",\n                                children: \"Add cover image\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        rows: 1,\n                        placeholder: \"Post Title\",\n                        ref: textareaRef,\n                        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().headline),\n                        value: headline,\n                        onChange: handleChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        \"data-id\": \"0\",\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n                        onClick: triggerMenu,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"plus-circle\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__heading--h6\",\n                                children: \"Add block\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 219,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Dnd__WEBPACK_IMPORTED_MODULE_14__.Board, {\n                initial: normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.components,\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnComponent, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnAddBlock, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 242,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentMenu__WEBPACK_IMPORTED_MODULE_15__.ComponentMenu, {\n                trigger: menu,\n                onClose: handleAddBlock\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 247,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n        lineNumber: 218,\n        columnNumber: 3\n    }, _this);\n};\n_s(LayoutEditor, \"Lwv10dbcrNmFEGn3WObpwHKLwJY=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayoutEditor;\nvar _c;\n$RefreshReg$(_c, \"LayoutEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx\n"));

/***/ })

});