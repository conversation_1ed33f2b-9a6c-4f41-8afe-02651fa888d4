"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Selection: function() { return /* binding */ Selection; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _selection_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./selection.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/selection.module.scss\");\n/* harmony import */ var _selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_selection_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nvar Selection = function(props) {\n    _s();\n    var required = props.required, value = props.value, onChange = props.onChange, name = props.name, placeholder = props.placeholder, enumOptions = props[\"enum\"], options = props.options;\n    var selectionMode = Array.isArray(value) ? \"multiple\" : \"single\";\n    var reformatedValue = function() {\n        // Handle null or undefined value\n        if (value === null || value === undefined) {\n            // For multiple selection, return empty array\n            if (selectionMode === \"multiple\") {\n                return [];\n            }\n            // For single selection, return undefined to use placeholder\n            return undefined;\n        }\n        // Handle array values\n        if (Array.isArray(value)) {\n            return value.map(function(v) {\n                return {\n                    label: v,\n                    value: v\n                };\n            });\n        }\n        // Handle single value\n        return {\n            label: value,\n            value: value\n        };\n    };\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(reformatedValue), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    (0,_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)(function() {\n        setPropsValue(reformatedValue);\n    }, [\n        value\n    ]);\n    // Determine if the component should be disabled (when none select item or null  value)\n    var isDisabled = (enumOptions || options).length === 0 || value === null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper), isDisabled && (_selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default().disabled)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.Select, {\n            className: \"collect__input has__border\",\n            required: required,\n            mode: selectionMode,\n            endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                type: \"cms\",\n                variant: \"chevron-down\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                lineNumber: 57,\n                columnNumber: 14\n            }, void 0),\n            placeholder: isDisabled ? \"Not available\" : placeholder,\n            defaultOption: propsValue,\n            onChange: function(e) {\n                // If disabled, don't process changes\n                if (isDisabled) return;\n                var values = Array.isArray(e) ? e.map(function(item) {\n                    return item.value;\n                }) : e === null || e === void 0 ? void 0 : e.value;\n                onChange === null || onChange === void 0 ? void 0 : onChange({\n                    field: name,\n                    value: selectionMode === \"multiple\" ? values : values\n                });\n            },\n            children: isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                value: \"Not available\",\n                children: \"Not available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                lineNumber: 73,\n                columnNumber: 6\n            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (enumOptions || options).map(function(option, idx) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                        value: option,\n                        children: option\n                    }, idx, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 8\n                    }, _this);\n                })\n            }, void 0, false)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n            lineNumber: 53,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, _this);\n};\n_s(Selection, \"QbBeCgdSr7y1WxEUQqhuk7n4XlA=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Selection;\nvar _c;\n$RefreshReg$(_c, \"Selection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx\n"));

/***/ })

});