"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Selection: function() { return /* binding */ Selection; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Select,SelectItem,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _selection_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./selection.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/selection.module.scss\");\n/* harmony import */ var _selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_selection_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nvar Selection = function(props) {\n    _s();\n    var required = props.required, value = props.value, onChange = props.onChange, name = props.name, placeholder = props.placeholder, enumOptions = props[\"enum\"], options = props.options;\n    var selectionMode = Array.isArray(value) ? \"multiple\" : \"single\";\n    // console.log(value)\n    var reformatedValue = function() {\n        // Handle null or undefined value\n        if (value === null || value === undefined) {\n            // For multiple selection, return empty array\n            if (selectionMode === \"multiple\") {\n                return [];\n            }\n            // For single selection, return undefined to use placeholder\n            return undefined;\n        }\n        // Handle array values\n        if (Array.isArray(value)) {\n            return value.map(function(v) {\n                return {\n                    label: v,\n                    value: v\n                };\n            });\n        }\n        // Handle single value\n        return {\n            label: value,\n            value: value\n        };\n    };\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(reformatedValue), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    (0,_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)(function() {\n        setPropsValue(reformatedValue);\n    }, [\n        value\n    ]);\n    // Determine if the component should be disabled (when none select item)\n    var isDisabled = (enumOptions || options).length === 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper), isDisabled && (_selection_module_scss__WEBPACK_IMPORTED_MODULE_3___default().disabled)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.Select, {\n            className: \"collect__input has__border\",\n            required: required,\n            mode: selectionMode,\n            endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                type: \"cms\",\n                variant: \"chevron-down\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                lineNumber: 58,\n                columnNumber: 14\n            }, void 0),\n            placeholder: isDisabled ? \"Not available\" : placeholder,\n            defaultOption: propsValue,\n            onChange: function(e) {\n                // If disabled, don't process changes\n                if (isDisabled) return;\n                var values = Array.isArray(e) ? e.map(function(item) {\n                    return item.value;\n                }) : e === null || e === void 0 ? void 0 : e.value;\n                onChange === null || onChange === void 0 ? void 0 : onChange({\n                    field: name,\n                    value: selectionMode === \"multiple\" ? values : values\n                });\n            },\n            children: isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                value: \"Not available\",\n                children: \"Not available\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                lineNumber: 74,\n                columnNumber: 6\n            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (enumOptions || options).map(function(option, idx) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                        value: option,\n                        children: option\n                    }, idx, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 8\n                    }, _this);\n                })\n            }, void 0, false)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n            lineNumber: 54,\n            columnNumber: 4\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Selection\\\\Selection.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, _this);\n};\n_s(Selection, \"QbBeCgdSr7y1WxEUQqhuk7n4XlA=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_Select_SelectItem_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Selection;\nvar _c;\n$RefreshReg$(_c, \"Selection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Selection/Selection.tsx\n"));

/***/ })

});