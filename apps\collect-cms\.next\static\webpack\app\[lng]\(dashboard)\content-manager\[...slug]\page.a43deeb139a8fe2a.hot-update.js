"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/LayoutEditor.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutEditor: function() { return /* binding */ LayoutEditor; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useWindowDimensions.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @collective/ui-lib/src/base/Wrapper */ \"(app-pages-browser)/../../packages/ui-lib/src/base/Wrapper.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _ComponentMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../ComponentMenu */ \"(app-pages-browser)/./src/components/Builder/ComponentMenu/ComponentMenu.tsx\");\n/* harmony import */ var _ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ComponentQuickActions */ \"(app-pages-browser)/./src/components/Builder/ComponentQuickActions/ComponentQuickActions.tsx\");\n/* harmony import */ var _Dnd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../Dnd */ \"(app-pages-browser)/./src/components/Builder/Dnd/Board.tsx\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layouteditor.module.scss */ \"(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar LayoutEditor = function(param) {\n    var children = param.children;\n    _s();\n    var _s1 = $RefreshSig$();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var data = context.data, contentType = context.contentType, configuration = context.configuration, setData = context.setData, components = context.components, setEditingIden = context.setEditingIden, normalizedData = context.normalizedData, setChildComponentData = context.setChildComponentData;\n    var _ref = data !== null && data !== void 0 ? data : {}, commonData = _ref.data;\n    var _ref1 = contentType !== null && contentType !== void 0 ? contentType : {}, uidConfig = _ref1.data;\n    var _ref2 = components !== null && components !== void 0 ? components : {}, uiConfig = _ref2.data;\n    var windowDimension = (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions)();\n    // Handle Headline change\n    var globalField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return \"\";\n        var settings = configuration.data.contentType.settings;\n        var mainFieldKey = settings.mainField;\n        return mainFieldKey;\n    }, [\n        contentType,\n        configuration\n    ]);\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(commonData && globalField ? commonData[globalField] : \"\"), 2), headline = _useState[0], setHeadline = _useState[1];\n    var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleChange = function(event) {\n        if (!textareaRef.current) return;\n        var target = event.target;\n        setHeadline(target.value);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__._)({}, globalField, target.value))\n        }));\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (!textareaRef.current) return;\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    }, [\n        textareaRef,\n        windowDimension\n    ]);\n    // Handle component menu\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), 2), menu = _useState1[0], setMenu = _useState1[1];\n    var triggerMenu = function(e) {\n        var container = e.currentTarget;\n        setMenu(function(prev) {\n            return prev !== container ? container : null;\n        });\n    };\n    var handleAddBlock = function(component) {\n        if (!component) return setMenu(null);\n        var id1 = Number(menu === null || menu === void 0 ? void 0 : menu.dataset.id);\n        var defaultData = uiConfig.find(function(item) {\n            return item.uid === component.uid;\n        });\n        var attributes = defaultData === null || defaultData === void 0 ? void 0 : defaultData.schema.attributes;\n        if (!attributes) return setMenu(null);\n        var remapProps = Object.entries(attributes).reduce(function(acc, param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n            var newValue;\n            switch(value.type){\n                case \"boolean\":\n                    var _value_default;\n                    newValue = (_value_default = value[\"default\"]) !== null && _value_default !== void 0 ? _value_default : false;\n                    break;\n                case \"string\":\n                    newValue = \"\";\n                    break;\n                default:\n                    newValue = null;\n                    break;\n            }\n            acc[key] = newValue;\n            return acc;\n        }, {});\n        var addData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n            __component: component.uid\n        }, remapProps), {\n            __temp_key__: normalizedData.components.length + 1\n        });\n        var components = normalizedData.components;\n        var index = components.findIndex(function(component) {\n            return component.__temp_key__ === id1;\n        });\n        components.splice(index + 1, 0, addData);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), {\n                components: components\n            })\n        }));\n        setMenu(null);\n    };\n    // Get list available components\n    var Modules = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!uidConfig) return {};\n        if (!uidConfig.schema.attributes.components) return {};\n        if (\"components\" in uidConfig.schema.attributes.components === false) return {};\n        var components = {};\n        var arrComponents = uidConfig.schema.attributes.components.components;\n        arrComponents === null || arrComponents === void 0 ? void 0 : arrComponents.forEach(function(module) {\n            var Component = (0,_collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__.CmsWrapper)({\n                module: module\n            });\n            if (Component && components) components[module] = Component;\n        });\n        return components;\n    }, [\n        uidConfig\n    ]);\n    // Component wrapper with hover state\n    var ComponentWrapper = function(param) {\n        var column = param.column, index = param.index;\n        _s1();\n        var Module = (column === null || column === void 0 ? void 0 : column.__component) && Modules && Modules[column.__component];\n        var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isHovered = _useState[0], setIsHovered = _useState[1];\n        var handleEdit = function() {\n            if (!column) return;\n            var _$id = column.id || column.__temp_key__;\n            setEditingIden({\n                key: column.__component,\n                id: _$id\n            });\n        // setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })\n        };\n        console.log(id, Module);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            tabIndex: 0,\n            role: \"button\",\n            className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__block),\n            onClick: handleEdit,\n            onKeyDown: handleEdit,\n            onMouseEnter: function() {\n                return setIsHovered(true);\n            },\n            onMouseLeave: function() {\n                return setIsHovered(false);\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__.ComponentQuickActions, {\n                    index: index,\n                    id: column === null || column === void 0 ? void 0 : column.__temp_key__,\n                    isVisible: isHovered\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 5\n                }, _this),\n                Module ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Module, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, column), void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__error)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, column === null || column === void 0 ? void 0 : column.__temp_key__, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 159,\n            columnNumber: 4\n        }, _this);\n    };\n    _s1(ComponentWrapper, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n    // Column component for Board\n    var ColumnComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComponentWrapper, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, props), void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 190,\n            columnNumber: 11\n        }, _this);\n    }, // ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        Modules,\n        setEditingIden\n    ]);\n    var ColumnAddBlock = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(param) {\n        var column = param.column;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            \"data-id\": column === null || column === void 0 ? void 0 : column.__temp_key__,\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n            onClick: triggerMenu,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                    variant: \"plus-circle\",\n                    type: \"cms\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().line)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 199,\n            columnNumber: 4\n        }, _this);\n    }, []);\n    // Toggle active when trigger menu\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        var allBlockBtn = document.querySelectorAll(\".add__block\");\n        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n        try {\n            for(var _iterator = allBlockBtn[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                var button = _step.value;\n                button.classList.toggle(\"active\", menu === button);\n            }\n        } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n        } finally{\n            try {\n                if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                    _iterator[\"return\"]();\n                }\n            } finally{\n                if (_didIteratorError) {\n                    throw _iteratorError;\n                }\n            }\n        }\n    }, [\n        menu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__image)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"image\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__body--lg\",\n                                children: \"Add cover image\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        rows: 1,\n                        placeholder: \"Post Title\",\n                        ref: textareaRef,\n                        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().headline),\n                        value: headline,\n                        onChange: handleChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        \"data-id\": \"0\",\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n                        onClick: triggerMenu,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"plus-circle\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__heading--h6\",\n                                children: \"Add block\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 220,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Dnd__WEBPACK_IMPORTED_MODULE_14__.Board, {\n                initial: normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.components,\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnComponent, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnAddBlock, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 243,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentMenu__WEBPACK_IMPORTED_MODULE_15__.ComponentMenu, {\n                trigger: menu,\n                onClose: handleAddBlock\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 248,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n        lineNumber: 219,\n        columnNumber: 3\n    }, _this);\n};\n_s(LayoutEditor, \"Lwv10dbcrNmFEGn3WObpwHKLwJY=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayoutEditor;\nvar _c;\n$RefreshReg$(_c, \"LayoutEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0J1aWxkZXIvTGF5b3V0RWRpdG9yL0xheW91dEVkaXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVGO0FBRXZCO0FBQ3JDO0FBUWI7QUFDNEQ7QUFDMUI7QUFDZ0I7QUFDbEM7QUFDaUI7QUFFeEMsSUFBTWdCLGVBQWU7UUFBR0MsaUJBQUFBOzs7SUFDOUIsSUFBTUMsVUFBVVgsaURBQVVBLENBQUNJLHdFQUFrQkE7SUFDN0MsSUFDQ1EsT0FRR0QsUUFSSEMsTUFDQUMsY0FPR0YsUUFQSEUsYUFDQUMsZ0JBTUdILFFBTkhHLGVBQ0FDLFVBS0dKLFFBTEhJLFNBQ0FDLGFBSUdMLFFBSkhLLFlBQ0FDLGlCQUdHTixRQUhITSxnQkFDQUMsaUJBRUdQLFFBRkhPLGdCQUNBQyx3QkFDR1IsUUFESFE7SUFFRCxJQUE2QlAsT0FBQUEsaUJBQUFBLGtCQUFBQSxPQUFRLENBQUMsR0FBOUJBLGFBQXFCQSxLQUFyQkE7SUFDUixJQUE0QkMsUUFBQUEsd0JBQUFBLHlCQUFBQSxjQUFlLENBQUMsR0FBcENELFlBQW9CQyxNQUFwQkQ7SUFDUixJQUEyQkksUUFBQUEsdUJBQUFBLHdCQUFBQSxhQUFjLENBQUMsR0FBbENKLFdBQW1CSSxNQUFuQko7SUFFUixJQUFNVyxrQkFBa0I1Qiw4SUFBbUJBO0lBRTNDLHlCQUF5QjtJQUN6QixJQUFNNkIsY0FBY3ZCLDhDQUFPQSxDQUFDO1FBQzNCLElBQUksQ0FBQ1ksWUFBWUQsSUFBSSxJQUFJLENBQUNFLGNBQWNGLElBQUksRUFBRSxPQUFPO1FBQ3JELElBQU0sV0FBZUUsY0FBY0YsSUFBSSxDQUFDQyxXQUFXLENBQTNDWTtRQUNSLElBQU1DLGVBQWVELFNBQVNFLFNBQVM7UUFDdkMsT0FBT0Q7SUFDUixHQUFHO1FBQUNiO1FBQWFDO0tBQWM7SUFFL0IsSUFBZ0NYLFlBQUFBLCtEQUFBQSxDQUFBQSwrQ0FBUUEsQ0FDdkNpQixjQUFjSSxjQUFlSixVQUFVLENBQUNJLFlBQXFCLEdBQWMsU0FEckVJLFdBQXlCekIsY0FBZjBCLGNBQWUxQjtJQUdoQyxJQUFNMkIsY0FBYzVCLDZDQUFNQSxDQUFzQjtJQUVoRCxJQUFNNkIsZUFBZSxTQUFDQztRQUNyQixJQUFJLENBQUNGLFlBQVlHLE9BQU8sRUFBRTtRQUMxQixJQUFNQyxTQUFTRixNQUFNRSxNQUFNO1FBQzNCTCxZQUFZSyxPQUFPQyxLQUFLO1FBQ3hCcEIsUUFBUSxzSUFDSkg7WUFDSEEsTUFBTSxzSUFDRkEsS0FBS0EsSUFBSSxHQUNaLG9FQUFDWSxhQUFjVSxPQUFPQyxLQUFLOztRQUc3QkwsWUFBWUcsT0FBTyxDQUFDRyxLQUFLLENBQUNDLE1BQU0sR0FBRztRQUNuQ1AsWUFBWUcsT0FBTyxDQUFDRyxLQUFLLENBQUNDLE1BQU0sR0FBR1AsWUFBWUcsT0FBTyxDQUFDSyxZQUFZLEdBQUc7SUFDdkU7SUFFQTVDLHFKQUF5QkEsQ0FBQztRQUN6QixJQUFJLENBQUNvQyxZQUFZRyxPQUFPLEVBQUU7UUFDMUJILFlBQVlHLE9BQU8sQ0FBQ0csS0FBSyxDQUFDQyxNQUFNLEdBQUc7UUFDbkNQLFlBQVlHLE9BQU8sQ0FBQ0csS0FBSyxDQUFDQyxNQUFNLEdBQUdQLFlBQVlHLE9BQU8sQ0FBQ0ssWUFBWSxHQUFHO0lBQ3ZFLEdBQUc7UUFBQ1I7UUFBYVA7S0FBZ0I7SUFFakMsd0JBQXdCO0lBQ3hCLElBQXdCcEIsYUFBQUEsK0RBQUFBLENBQUFBLCtDQUFRQSxDQUFxQixXQUE5Q29DLE9BQWlCcEMsZUFBWHFDLFVBQVdyQztJQUN4QixJQUFNc0MsY0FBYyxTQUFDQztRQUNwQixJQUFNQyxZQUFZRCxFQUFFRSxhQUFhO1FBRWpDSixRQUFRLFNBQUNLO1lBQ1IsT0FBT0EsU0FBU0YsWUFBWUEsWUFBWTtRQUN6QztJQUNEO0lBRUEsSUFBTUcsaUJBQWlCLFNBQUNDO1FBQ3ZCLElBQUksQ0FBQ0EsV0FBVyxPQUFPUCxRQUFRO1FBQy9CLElBQU1RLE1BQUtDLE9BQU9WLGlCQUFBQSwyQkFBQUEsS0FBTVcsT0FBTyxDQUFDRixFQUFFO1FBQ2xDLElBQU1HLGNBQWM3QixTQUFTOEIsSUFBSSxDQUFDLFNBQUNDO21CQUFTQSxLQUFLQyxHQUFHLEtBQUtQLFVBQVVPLEdBQUc7O1FBQ3RFLElBQU1DLGFBQWFKLHdCQUFBQSxrQ0FBQUEsWUFBYUssTUFBTSxDQUFDRCxVQUFVO1FBRWpELElBQUksQ0FBQ0EsWUFBWSxPQUFPZixRQUFRO1FBQ2hDLElBQU1pQixhQUFhQyxPQUFPQyxPQUFPLENBQUNKLFlBQVlLLE1BQU0sQ0FDbkQsU0FBQ0M7b0dBQU1DLGlCQUFLM0I7WUFDWCxJQUFJNEI7WUFFSixPQUFRNUIsTUFBTTZCLElBQUk7Z0JBQ2pCLEtBQUs7d0JBQ083QjtvQkFBWDRCLFdBQVc1QixDQUFBQSxpQkFBQUEsS0FBTThCLENBQUFBLFVBQU8sY0FBYjlCLDRCQUFBQSxpQkFBaUI7b0JBQzVCO2dCQUNELEtBQUs7b0JBQ0o0QixXQUFXO29CQUNYO2dCQUNEO29CQUNDQSxXQUFXO29CQUNYO1lBQ0Y7WUFFQUYsR0FBRyxDQUFDQyxJQUFJLEdBQUdDO1lBQ1gsT0FBT0Y7UUFDUixHQUNBLENBQUM7UUFHRixJQUFNSyxVQUFVO1lBQ2ZDLGFBQWFwQixVQUFVTyxHQUFHO1dBQ3ZCRztZQUNIVyxjQUFjbEQsZUFBZUYsVUFBVSxDQUFDcUQsTUFBTSxHQUFHOztRQUdsRCxJQUFNckQsYUFBYUUsZUFBZUYsVUFBVTtRQUM1QyxJQUFNc0QsUUFBUXRELFdBQVd1RCxTQUFTLENBQUMsU0FBQ3hCO21CQUFjQSxVQUFVcUIsWUFBWSxLQUFLcEI7O1FBQzdFaEMsV0FBV3dELE1BQU0sQ0FBQ0YsUUFBUSxHQUFHLEdBQUdKO1FBRWhDbkQsUUFBUSxzSUFDSkg7WUFDSEEsTUFBTSxzSUFDRkEsS0FBS0EsSUFBSTtnQkFDWkksWUFBQUE7OztRQUdGd0IsUUFBUTtJQUNUO0lBRUEsZ0NBQWdDO0lBQ2hDLElBQU1pQyxVQUFVeEUsOENBQU9BLENBQUM7UUFDdkIsSUFBSSxDQUFDb0IsV0FBVyxPQUFPLENBQUM7UUFDeEIsSUFBSSxDQUFDQSxVQUFVbUMsTUFBTSxDQUFDRCxVQUFVLENBQUN2QyxVQUFVLEVBQUUsT0FBTyxDQUFDO1FBQ3JELElBQUksZ0JBQWdCSyxVQUFVbUMsTUFBTSxDQUFDRCxVQUFVLENBQUN2QyxVQUFVLEtBQUssT0FBTyxPQUFPLENBQUM7UUFDOUUsSUFBTUEsYUFFRixDQUFDO1FBQ0wsSUFBTTBELGdCQUFnQnJELFVBQVVtQyxNQUFNLENBQUNELFVBQVUsQ0FBQ3ZDLFVBQVUsQ0FBQ0EsVUFBVTtRQUN2RTBELDBCQUFBQSxvQ0FBQUEsY0FBZUMsT0FBTyxDQUFDLFNBQUNDO1lBQ3ZCLElBQU1DLFlBQVlqRixnRkFBVUEsQ0FBQztnQkFBRWdGLFFBQUFBO1lBQU87WUFDdEMsSUFBSUMsYUFBYTdELFlBQVlBLFVBQVUsQ0FBQzRELE9BQU8sR0FBR0M7UUFDbkQ7UUFFQSxPQUFPN0Q7SUFDUixHQUFHO1FBQUNLO0tBQVU7SUFFZCxxQ0FBcUM7SUFDckMsSUFBTXlELG1CQUFtQjtZQUFHQyxlQUFBQSxRQUFRVCxjQUFBQTs7UUFDbkMsSUFBTVUsU0FBU0QsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRWixXQUFXLEtBQUlNLFdBQVdBLE9BQU8sQ0FBQ00sT0FBT1osV0FBVyxDQUFDO1FBQzVFLElBQWtDaEUsWUFBQUEsK0RBQUFBLENBQUFBLCtDQUFRQSxDQUFDLFlBQXBDOEUsWUFBMkI5RSxjQUFoQitFLGVBQWdCL0U7UUFDbEMsSUFBTWdGLGFBQWE7WUFDbEIsSUFBSSxDQUFDSixRQUFRO1lBQ2IsSUFBTS9CLE9BQUsrQixPQUFPL0IsRUFBRSxJQUFJK0IsT0FBT1gsWUFBWTtZQUMzQ25ELGVBQWU7Z0JBQUU2QyxLQUFLaUIsT0FBT1osV0FBVztnQkFBWW5CLElBQUlBO1lBQWE7UUFDckUsOEVBQThFO1FBQy9FO1FBQ0FvQyxRQUFRQyxHQUFHLENBQUNyQyxJQUFJZ0M7UUFDaEIscUJBQ0MsOERBQUNNO1lBQ0FDLFVBQVU7WUFDVkMsTUFBSztZQUNMQyxXQUFXakYsbUZBQXVCO1lBRWxDbUYsU0FBU1I7WUFDVFMsV0FBV1Q7WUFDWFUsY0FBYzt1QkFBTVgsYUFBYTs7WUFDakNZLGNBQWM7dUJBQU1aLGFBQWE7Ozs4QkFFakMsOERBQUM1RSwwRUFBcUJBO29CQUNyQmdFLE9BQU9BO29CQUNQdEIsRUFBRSxFQUFFK0IsbUJBQUFBLDZCQUFBQSxPQUFRWCxZQUFZO29CQUN4QjJCLFdBQVdkOzs7Ozs7Z0JBRVhELHVCQUNBLDhEQUFDQSxRQUFBQSw2REFBQUEsS0FBV0Q7Ozs7MENBRVosOERBQUNPO29CQUFJRyxXQUFXakYsbUZBQXVCOzs7Ozs7O1dBZG5DdUUsbUJBQUFBLDZCQUFBQSxPQUFRWCxZQUFZOzs7OztJQXNCNUI7UUFyQ01VO0lBdUNOLDZCQUE2QjtJQUM3QixJQUFNbUIsa0JBQWtCbEcsa0RBQVdBLENBQ2xDLFNBQUNtRztRQUNBLHFCQUFPLDhEQUFDcEIsa0JBQUFBLDZEQUFBQSxLQUFxQm9COzs7OztJQUM5QixHQUNBLHVHQUF1RztJQUN2Ryx1REFBdUQ7SUFDdkQ7UUFBQ3pCO1FBQVN4RDtLQUFlO0lBRzFCLElBQU1rRixpQkFBaUJwRyxrREFBV0EsQ0FBQztZQUFHZ0YsZUFBQUE7UUFDckMscUJBQ0MsOERBQUNxQjtZQUNBQyxTQUFPLEVBQUV0QixtQkFBQUEsNkJBQUFBLE9BQVFYLFlBQVk7WUFDN0JxQixXQUFXNUYsaURBQUVBLENBQUMsMENBQTBDVyw2RUFBaUI7WUFDekVtRixTQUFTbEQ7OzhCQUVULDhEQUFDaEQsNEhBQUlBO29CQUFDOEcsU0FBUTtvQkFBY3ZDLE1BQUs7Ozs7Ozs4QkFDakMsOERBQUN3QztvQkFBS2YsV0FBV2pGLHVFQUFXOzs7Ozs7Ozs7Ozs7SUFHL0IsR0FBRyxFQUFFO0lBRUwsa0NBQWtDO0lBQ2xDZCxxSkFBeUJBLENBQUM7UUFDekIsSUFBTWdILGNBQWNDLFNBQVNDLGdCQUFnQixDQUFDO1lBQ3pDOztZQUFMLFFBQUssWUFBZ0JGLGdDQUFoQix3R0FBNkI7Z0JBQTdCLElBQU1OLFNBQU47Z0JBQ0pBLE9BQU9TLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDLFVBQVV2RSxTQUFTNkQ7WUFDNUM7O1lBRks7WUFBQTs7O3FCQUFBO29CQUFBOzs7b0JBQUE7MEJBQUE7Ozs7SUFHTixHQUFHO1FBQUM3RDtLQUFLO0lBRVQscUJBQ0MsOERBQUMrQztRQUFJRyxXQUFXakYsMEVBQWM7OzBCQUM3Qiw4REFBQzhFO2dCQUFJRyxXQUFXakYseUVBQWE7O2tDQUM1Qiw4REFBQzRGO3dCQUFPWCxXQUFXNUYsaURBQUVBLENBQUMsMENBQTBDVyw2RUFBaUI7OzBDQUNoRiw4REFBQ2YsNEhBQUlBO2dDQUFDOEcsU0FBUTtnQ0FBUXZDLE1BQUs7Ozs7OzswQ0FDM0IsOERBQUN3QztnQ0FBS2YsV0FBVTswQ0FBb0I7Ozs7Ozs7Ozs7OztrQ0FFckMsOERBQUN5Qjt3QkFDQUMsTUFBTTt3QkFDTkMsYUFBWTt3QkFDWkMsS0FBS3ZGO3dCQUNMMkQsV0FBV2pGLDJFQUFlO3dCQUMxQjJCLE9BQU9QO3dCQUNQMEYsVUFBVXZGOzs7Ozs7a0NBRVgsOERBQUNxRTt3QkFDQUMsV0FBUTt3QkFDUlosV0FBVzVGLGlEQUFFQSxDQUFDLDBDQUEwQ1csNkVBQWlCO3dCQUN6RW1GLFNBQVNsRDs7MENBRVQsOERBQUNoRCw0SEFBSUE7Z0NBQUM4RyxTQUFRO2dDQUFjdkMsTUFBSzs7Ozs7OzBDQUNqQyw4REFBQ3dDO2dDQUFLZixXQUFVOzBDQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUl6Qyw4REFBQ2xGLHdDQUFLQTtnQkFBQ2dILE9BQU8sRUFBRXJHLDJCQUFBQSxxQ0FBQUEsZUFBZ0JGLFVBQVU7Z0JBQXVCeUUsV0FBV2pGLHVFQUFXOztrQ0FDdEYsOERBQUN5Rjs7Ozs7a0NBQ0QsOERBQUNFOzs7Ozs7Ozs7OzswQkFHRiw4REFBQzlGLDBEQUFhQTtnQkFBQ29ILFNBQVNsRjtnQkFBTW1GLFNBQVM1RTs7Ozs7Ozs7Ozs7O0FBRzFDLEVBQUM7R0F4T1lyQzs7UUFnQllkLDBJQUFtQkE7UUE4QjNDRCxpSkFBeUJBO1FBa0p6QkEsaUpBQXlCQTs7O0tBaE1iZSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9CdWlsZGVyL0xheW91dEVkaXRvci9MYXlvdXRFZGl0b3IudHN4PzNlNGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSWNvbiwgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCwgdXNlV2luZG93RGltZW5zaW9ucyB9IGZyb20gJ0Bjb2xsZWN0aXZlL2NvcmUnXG5pbXBvcnQgdHlwZSB7IElDb21wb25lbnRQcm9wcyB9IGZyb20gJ0Bjb2xsZWN0aXZlL2ludGVncmF0aW9uLWxpYi9jbXMnXG5pbXBvcnQgeyBDbXNXcmFwcGVyIH0gZnJvbSAnQGNvbGxlY3RpdmUvdWktbGliL3NyYy9iYXNlL1dyYXBwZXInXG5pbXBvcnQgY24gZnJvbSAnY2xhc3NuYW1lcydcbmltcG9ydCBSZWFjdCwge1xuXHR1c2VDYWxsYmFjayxcblx0dXNlQ29udGV4dCxcblx0dXNlTWVtbyxcblx0dXNlUmVmLFxuXHR1c2VTdGF0ZSxcblx0dHlwZSBDb21wb25lbnRUeXBlLFxufSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFBhZ2VCdWlsZGVyQ29udGV4dCwgdHlwZSBFbnRyeSB9IGZyb20gJ0AvY29udGV4dHMvQnVpbGRlckNvbnRleHQnXG5pbXBvcnQgeyBDb21wb25lbnRNZW51IH0gZnJvbSAnLi4vQ29tcG9uZW50TWVudSdcbmltcG9ydCB7IENvbXBvbmVudFF1aWNrQWN0aW9ucyB9IGZyb20gJy4uL0NvbXBvbmVudFF1aWNrQWN0aW9ucydcbmltcG9ydCB7IEJvYXJkIH0gZnJvbSAnLi4vRG5kJ1xuaW1wb3J0IHN0eWxlcyBmcm9tICcuL2xheW91dGVkaXRvci5tb2R1bGUuc2NzcydcblxuZXhwb3J0IGNvbnN0IExheW91dEVkaXRvciA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiB7XG5cdGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFBhZ2VCdWlsZGVyQ29udGV4dClcblx0Y29uc3Qge1xuXHRcdGRhdGEsXG5cdFx0Y29udGVudFR5cGUsXG5cdFx0Y29uZmlndXJhdGlvbixcblx0XHRzZXREYXRhLFxuXHRcdGNvbXBvbmVudHMsXG5cdFx0c2V0RWRpdGluZ0lkZW4sXG5cdFx0bm9ybWFsaXplZERhdGEsXG5cdFx0c2V0Q2hpbGRDb21wb25lbnREYXRhLFxuXHR9ID0gY29udGV4dFxuXHRjb25zdCB7IGRhdGE6IGNvbW1vbkRhdGEgfSA9IGRhdGEgPz8ge31cblx0Y29uc3QgeyBkYXRhOiB1aWRDb25maWcgfSA9IGNvbnRlbnRUeXBlID8/IHt9XG5cdGNvbnN0IHsgZGF0YTogdWlDb25maWcgfSA9IGNvbXBvbmVudHMgPz8ge31cblxuXHRjb25zdCB3aW5kb3dEaW1lbnNpb24gPSB1c2VXaW5kb3dEaW1lbnNpb25zKClcblxuXHQvLyBIYW5kbGUgSGVhZGxpbmUgY2hhbmdlXG5cdGNvbnN0IGdsb2JhbEZpZWxkID0gdXNlTWVtbygoKSA9PiB7XG5cdFx0aWYgKCFjb250ZW50VHlwZS5kYXRhIHx8ICFjb25maWd1cmF0aW9uLmRhdGEpIHJldHVybiAnJ1xuXHRcdGNvbnN0IHsgc2V0dGluZ3MgfSA9IGNvbmZpZ3VyYXRpb24uZGF0YS5jb250ZW50VHlwZVxuXHRcdGNvbnN0IG1haW5GaWVsZEtleSA9IHNldHRpbmdzLm1haW5GaWVsZFxuXHRcdHJldHVybiBtYWluRmllbGRLZXlcblx0fSwgW2NvbnRlbnRUeXBlLCBjb25maWd1cmF0aW9uXSlcblxuXHRjb25zdCBbaGVhZGxpbmUsIHNldEhlYWRsaW5lXSA9IHVzZVN0YXRlPHN0cmluZz4oXG5cdFx0Y29tbW9uRGF0YSAmJiBnbG9iYWxGaWVsZCA/IChjb21tb25EYXRhW2dsb2JhbEZpZWxkIGFzIG5ldmVyXSBhcyBzdHJpbmcpIDogJydcblx0KVxuXHRjb25zdCB0ZXh0YXJlYVJlZiA9IHVzZVJlZjxIVE1MVGV4dEFyZWFFbGVtZW50PihudWxsKVxuXG5cdGNvbnN0IGhhbmRsZUNoYW5nZSA9IChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQpID0+IHtcblx0XHRpZiAoIXRleHRhcmVhUmVmLmN1cnJlbnQpIHJldHVyblxuXHRcdGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MVGV4dEFyZWFFbGVtZW50XG5cdFx0c2V0SGVhZGxpbmUodGFyZ2V0LnZhbHVlKVxuXHRcdHNldERhdGEoe1xuXHRcdFx0Li4uZGF0YSxcblx0XHRcdGRhdGE6IHtcblx0XHRcdFx0Li4uZGF0YS5kYXRhLFxuXHRcdFx0XHRbZ2xvYmFsRmllbGRdOiB0YXJnZXQudmFsdWUsXG5cdFx0XHR9LFxuXHRcdH0pXG5cdFx0dGV4dGFyZWFSZWYuY3VycmVudC5zdHlsZS5oZWlnaHQgPSAnYXV0bydcblx0XHR0ZXh0YXJlYVJlZi5jdXJyZW50LnN0eWxlLmhlaWdodCA9IHRleHRhcmVhUmVmLmN1cnJlbnQuc2Nyb2xsSGVpZ2h0ICsgJ3B4J1xuXHR9XG5cblx0dXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG5cdFx0aWYgKCF0ZXh0YXJlYVJlZi5jdXJyZW50KSByZXR1cm5cblx0XHR0ZXh0YXJlYVJlZi5jdXJyZW50LnN0eWxlLmhlaWdodCA9ICdhdXRvJ1xuXHRcdHRleHRhcmVhUmVmLmN1cnJlbnQuc3R5bGUuaGVpZ2h0ID0gdGV4dGFyZWFSZWYuY3VycmVudC5zY3JvbGxIZWlnaHQgKyAncHgnXG5cdH0sIFt0ZXh0YXJlYVJlZiwgd2luZG93RGltZW5zaW9uXSlcblxuXHQvLyBIYW5kbGUgY29tcG9uZW50IG1lbnVcblx0Y29uc3QgW21lbnUsIHNldE1lbnVdID0gdXNlU3RhdGU8SFRNTEVsZW1lbnQgfCBudWxsPihudWxsKVxuXHRjb25zdCB0cmlnZ2VyTWVudSA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG5cdFx0Y29uc3QgY29udGFpbmVyID0gZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxFbGVtZW50XG5cblx0XHRzZXRNZW51KChwcmV2KSA9PiB7XG5cdFx0XHRyZXR1cm4gcHJldiAhPT0gY29udGFpbmVyID8gY29udGFpbmVyIDogbnVsbFxuXHRcdH0pXG5cdH1cblxuXHRjb25zdCBoYW5kbGVBZGRCbG9jayA9IChjb21wb25lbnQ/OiBJQ29tcG9uZW50UHJvcHMpID0+IHtcblx0XHRpZiAoIWNvbXBvbmVudCkgcmV0dXJuIHNldE1lbnUobnVsbClcblx0XHRjb25zdCBpZCA9IE51bWJlcihtZW51Py5kYXRhc2V0LmlkKVxuXHRcdGNvbnN0IGRlZmF1bHREYXRhID0gdWlDb25maWcuZmluZCgoaXRlbSkgPT4gaXRlbS51aWQgPT09IGNvbXBvbmVudC51aWQpXG5cdFx0Y29uc3QgYXR0cmlidXRlcyA9IGRlZmF1bHREYXRhPy5zY2hlbWEuYXR0cmlidXRlc1xuXG5cdFx0aWYgKCFhdHRyaWJ1dGVzKSByZXR1cm4gc2V0TWVudShudWxsKVxuXHRcdGNvbnN0IHJlbWFwUHJvcHMgPSBPYmplY3QuZW50cmllcyhhdHRyaWJ1dGVzKS5yZWR1Y2U8UmVjb3JkPHN0cmluZywgdW5rbm93bj4+KFxuXHRcdFx0KGFjYywgW2tleSwgdmFsdWVdKSA9PiB7XG5cdFx0XHRcdGxldCBuZXdWYWx1ZVxuXG5cdFx0XHRcdHN3aXRjaCAodmFsdWUudHlwZSkge1xuXHRcdFx0XHRcdGNhc2UgJ2Jvb2xlYW4nOlxuXHRcdFx0XHRcdFx0bmV3VmFsdWUgPSB2YWx1ZS5kZWZhdWx0ID8/IGZhbHNlXG5cdFx0XHRcdFx0XHRicmVha1xuXHRcdFx0XHRcdGNhc2UgJ3N0cmluZyc6XG5cdFx0XHRcdFx0XHRuZXdWYWx1ZSA9ICcnXG5cdFx0XHRcdFx0XHRicmVha1xuXHRcdFx0XHRcdGRlZmF1bHQ6XG5cdFx0XHRcdFx0XHRuZXdWYWx1ZSA9IG51bGxcblx0XHRcdFx0XHRcdGJyZWFrXG5cdFx0XHRcdH1cblxuXHRcdFx0XHRhY2Nba2V5XSA9IG5ld1ZhbHVlXG5cdFx0XHRcdHJldHVybiBhY2Ncblx0XHRcdH0sXG5cdFx0XHR7fVxuXHRcdClcblxuXHRcdGNvbnN0IGFkZERhdGEgPSB7XG5cdFx0XHRfX2NvbXBvbmVudDogY29tcG9uZW50LnVpZCBhcyBzdHJpbmcsXG5cdFx0XHQuLi5yZW1hcFByb3BzLFxuXHRcdFx0X190ZW1wX2tleV9fOiBub3JtYWxpemVkRGF0YS5jb21wb25lbnRzLmxlbmd0aCArIDEsXG5cdFx0fVxuXG5cdFx0Y29uc3QgY29tcG9uZW50cyA9IG5vcm1hbGl6ZWREYXRhLmNvbXBvbmVudHNcblx0XHRjb25zdCBpbmRleCA9IGNvbXBvbmVudHMuZmluZEluZGV4KChjb21wb25lbnQpID0+IGNvbXBvbmVudC5fX3RlbXBfa2V5X18gPT09IGlkKVxuXHRcdGNvbXBvbmVudHMuc3BsaWNlKGluZGV4ICsgMSwgMCwgYWRkRGF0YSlcblxuXHRcdHNldERhdGEoe1xuXHRcdFx0Li4uZGF0YSxcblx0XHRcdGRhdGE6IHtcblx0XHRcdFx0Li4uZGF0YS5kYXRhLFxuXHRcdFx0XHRjb21wb25lbnRzLFxuXHRcdFx0fSxcblx0XHR9KVxuXHRcdHNldE1lbnUobnVsbClcblx0fVxuXG5cdC8vIEdldCBsaXN0IGF2YWlsYWJsZSBjb21wb25lbnRzXG5cdGNvbnN0IE1vZHVsZXMgPSB1c2VNZW1vKCgpID0+IHtcblx0XHRpZiAoIXVpZENvbmZpZykgcmV0dXJuIHt9XG5cdFx0aWYgKCF1aWRDb25maWcuc2NoZW1hLmF0dHJpYnV0ZXMuY29tcG9uZW50cykgcmV0dXJuIHt9XG5cdFx0aWYgKCdjb21wb25lbnRzJyBpbiB1aWRDb25maWcuc2NoZW1hLmF0dHJpYnV0ZXMuY29tcG9uZW50cyA9PT0gZmFsc2UpIHJldHVybiB7fVxuXHRcdGNvbnN0IGNvbXBvbmVudHM6IHtcblx0XHRcdFtrZXk6IHN0cmluZ106IENvbXBvbmVudFR5cGU8SUNvbXBvbmVudFByb3BzPlxuXHRcdH0gPSB7fVxuXHRcdGNvbnN0IGFyckNvbXBvbmVudHMgPSB1aWRDb25maWcuc2NoZW1hLmF0dHJpYnV0ZXMuY29tcG9uZW50cy5jb21wb25lbnRzXG5cdFx0YXJyQ29tcG9uZW50cz8uZm9yRWFjaCgobW9kdWxlKSA9PiB7XG5cdFx0XHRjb25zdCBDb21wb25lbnQgPSBDbXNXcmFwcGVyKHsgbW9kdWxlIH0pXG5cdFx0XHRpZiAoQ29tcG9uZW50ICYmIGNvbXBvbmVudHMpIGNvbXBvbmVudHNbbW9kdWxlXSA9IENvbXBvbmVudFxuXHRcdH0pXG5cblx0XHRyZXR1cm4gY29tcG9uZW50c1xuXHR9LCBbdWlkQ29uZmlnXSlcblxuXHQvLyBDb21wb25lbnQgd3JhcHBlciB3aXRoIGhvdmVyIHN0YXRlXG5cdGNvbnN0IENvbXBvbmVudFdyYXBwZXIgPSAoeyBjb2x1bW4sIGluZGV4IH06IHsgY29sdW1uPzogSUNvbXBvbmVudFByb3BzOyBpbmRleD86IG51bWJlciB9KSA9PiB7XG5cdFx0Y29uc3QgTW9kdWxlID0gY29sdW1uPy5fX2NvbXBvbmVudCAmJiBNb2R1bGVzICYmIE1vZHVsZXNbY29sdW1uLl9fY29tcG9uZW50XVxuXHRcdGNvbnN0IFtpc0hvdmVyZWQsIHNldElzSG92ZXJlZF0gPSB1c2VTdGF0ZShmYWxzZSlcblx0XHRjb25zdCBoYW5kbGVFZGl0ID0gKCkgPT4ge1xuXHRcdFx0aWYgKCFjb2x1bW4pIHJldHVyblxuXHRcdFx0Y29uc3QgaWQgPSBjb2x1bW4uaWQgfHwgY29sdW1uLl9fdGVtcF9rZXlfX1xuXHRcdFx0c2V0RWRpdGluZ0lkZW4oeyBrZXk6IGNvbHVtbi5fX2NvbXBvbmVudCBhcyBzdHJpbmcsIGlkOiBpZCBhcyBudW1iZXIgfSlcblx0XHRcdC8vIHNldENoaWxkQ29tcG9uZW50RGF0YSh7IG5hbWU6ICcnLCB2YWx1ZToge30sIGZpZWxkczogW1snJywge31dIGFzIEVudHJ5XSB9KVxuXHRcdH1cblx0XHRjb25zb2xlLmxvZyhpZCwgTW9kdWxlKVxuXHRcdHJldHVybiAoXG5cdFx0XHQ8ZGl2XG5cdFx0XHRcdHRhYkluZGV4PXswfVxuXHRcdFx0XHRyb2xlPVwiYnV0dG9uXCJcblx0XHRcdFx0Y2xhc3NOYW1lPXtzdHlsZXMuY29tcG9uZW50X19ibG9ja31cblx0XHRcdFx0a2V5PXtjb2x1bW4/Ll9fdGVtcF9rZXlfX31cblx0XHRcdFx0b25DbGljaz17aGFuZGxlRWRpdH1cblx0XHRcdFx0b25LZXlEb3duPXtoYW5kbGVFZGl0fVxuXHRcdFx0XHRvbk1vdXNlRW50ZXI9eygpID0+IHNldElzSG92ZXJlZCh0cnVlKX1cblx0XHRcdFx0b25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRJc0hvdmVyZWQoZmFsc2UpfVxuXHRcdFx0PlxuXHRcdFx0XHQ8Q29tcG9uZW50UXVpY2tBY3Rpb25zXG5cdFx0XHRcdFx0aW5kZXg9e2luZGV4fVxuXHRcdFx0XHRcdGlkPXtjb2x1bW4/Ll9fdGVtcF9rZXlfXyBhcyBudW1iZXJ9XG5cdFx0XHRcdFx0aXNWaXNpYmxlPXtpc0hvdmVyZWR9XG5cdFx0XHRcdC8+XG5cdFx0XHRcdHtNb2R1bGUgPyAoXG5cdFx0XHRcdFx0PE1vZHVsZSB7Li4uY29sdW1ufSAvPlxuXHRcdFx0XHQpIDogKFxuXHRcdFx0XHRcdDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuY29tcG9uZW50X19lcnJvcn0+XG5cdFx0XHRcdFx0XHR7LyogPHA+XG5cdFx0XHRcdFx0XHRcdENvbXBvbmVudHMgPGI+e2NvbHVtbj8uX19jb21wb25lbnR9PC9iPiBmYWlsZWQgdG8gaW1wb3J0L2xvYWRcblx0XHRcdFx0XHRcdDwvcD4gKi99XG5cdFx0XHRcdFx0PC9kaXY+XG5cdFx0XHRcdCl9XG5cdFx0XHQ8L2Rpdj5cblx0XHQpXG5cdH1cblxuXHQvLyBDb2x1bW4gY29tcG9uZW50IGZvciBCb2FyZFxuXHRjb25zdCBDb2x1bW5Db21wb25lbnQgPSB1c2VDYWxsYmFjayhcblx0XHQocHJvcHM6IHsgY29sdW1uPzogSUNvbXBvbmVudFByb3BzOyBpbmRleD86IG51bWJlciB9KSA9PiB7XG5cdFx0XHRyZXR1cm4gPENvbXBvbmVudFdyYXBwZXIgey4uLnByb3BzfSAvPlxuXHRcdH0sXG5cdFx0Ly8gQ29tcG9uZW50V3JhcHBlciBpcyBkZWZpbmVkIGluIHRoZSBjb21wb25lbnQgc2NvcGUsIHNvIGl0IGRvZXNuJ3QgbmVlZCB0byBiZSBpbiB0aGUgZGVwZW5kZW5jeSBhcnJheVxuXHRcdC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcblx0XHRbTW9kdWxlcywgc2V0RWRpdGluZ0lkZW5dXG5cdClcblxuXHRjb25zdCBDb2x1bW5BZGRCbG9jayA9IHVzZUNhbGxiYWNrKCh7IGNvbHVtbiB9OiB7IGNvbHVtbj86IElDb21wb25lbnRQcm9wcyB9KSA9PiB7XG5cdFx0cmV0dXJuIChcblx0XHRcdDxidXR0b25cblx0XHRcdFx0ZGF0YS1pZD17Y29sdW1uPy5fX3RlbXBfa2V5X199XG5cdFx0XHRcdGNsYXNzTmFtZT17Y24oJ3RleHRfX3ctLWljb24gYWxpZ25fX2NlbnRlciBhZGRfX2Jsb2NrJywgc3R5bGVzLmFkZF9fYmxvY2spfVxuXHRcdFx0XHRvbkNsaWNrPXt0cmlnZ2VyTWVudX1cblx0XHRcdD5cblx0XHRcdFx0PEljb24gdmFyaWFudD1cInBsdXMtY2lyY2xlXCIgdHlwZT1cImNtc1wiIC8+XG5cdFx0XHRcdDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLmxpbmV9IC8+XG5cdFx0XHQ8L2J1dHRvbj5cblx0XHQpXG5cdH0sIFtdKVxuXG5cdC8vIFRvZ2dsZSBhY3RpdmUgd2hlbiB0cmlnZ2VyIG1lbnVcblx0dXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG5cdFx0Y29uc3QgYWxsQmxvY2tCdG4gPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuYWRkX19ibG9jaycpXG5cdFx0Zm9yIChjb25zdCBidXR0b24gb2YgYWxsQmxvY2tCdG4pIHtcblx0XHRcdGJ1dHRvbi5jbGFzc0xpc3QudG9nZ2xlKCdhY3RpdmUnLCBtZW51ID09PSBidXR0b24pXG5cdFx0fVxuXHR9LCBbbWVudV0pXG5cblx0cmV0dXJuIChcblx0XHQ8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLndyYXBwZXJ9PlxuXHRcdFx0PGRpdiBjbGFzc05hbWU9e3N0eWxlcy5oZWFkZXJ9PlxuXHRcdFx0XHQ8YnV0dG9uIGNsYXNzTmFtZT17Y24oJ3RleHRfX3ctLWljb24gYWxpZ25fX2NlbnRlciBhZGRfX2Jsb2NrJywgc3R5bGVzLmFkZF9faW1hZ2UpfT5cblx0XHRcdFx0XHQ8SWNvbiB2YXJpYW50PVwiaW1hZ2VcIiB0eXBlPVwiY21zXCIgLz5cblx0XHRcdFx0XHQ8c3BhbiBjbGFzc05hbWU9XCJjb2xsZWN0X19ib2R5LS1sZ1wiPkFkZCBjb3ZlciBpbWFnZTwvc3Bhbj5cblx0XHRcdFx0PC9idXR0b24+XG5cdFx0XHRcdDx0ZXh0YXJlYVxuXHRcdFx0XHRcdHJvd3M9ezF9XG5cdFx0XHRcdFx0cGxhY2Vob2xkZXI9XCJQb3N0IFRpdGxlXCJcblx0XHRcdFx0XHRyZWY9e3RleHRhcmVhUmVmfVxuXHRcdFx0XHRcdGNsYXNzTmFtZT17c3R5bGVzLmhlYWRsaW5lfVxuXHRcdFx0XHRcdHZhbHVlPXtoZWFkbGluZX1cblx0XHRcdFx0XHRvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuXHRcdFx0XHQvPlxuXHRcdFx0XHQ8YnV0dG9uXG5cdFx0XHRcdFx0ZGF0YS1pZD1cIjBcIlxuXHRcdFx0XHRcdGNsYXNzTmFtZT17Y24oJ3RleHRfX3ctLWljb24gYWxpZ25fX2NlbnRlciBhZGRfX2Jsb2NrJywgc3R5bGVzLmFkZF9fYmxvY2spfVxuXHRcdFx0XHRcdG9uQ2xpY2s9e3RyaWdnZXJNZW51fVxuXHRcdFx0XHQ+XG5cdFx0XHRcdFx0PEljb24gdmFyaWFudD1cInBsdXMtY2lyY2xlXCIgdHlwZT1cImNtc1wiIC8+XG5cdFx0XHRcdFx0PHNwYW4gY2xhc3NOYW1lPVwiY29sbGVjdF9faGVhZGluZy0taDZcIj5BZGQgYmxvY2s8L3NwYW4+XG5cdFx0XHRcdDwvYnV0dG9uPlxuXHRcdFx0PC9kaXY+XG5cdFx0XHR7LyogPGRpdj57Y2hpbGRyZW59PC9kaXY+ICovfVxuXHRcdFx0PEJvYXJkIGluaXRpYWw9e25vcm1hbGl6ZWREYXRhPy5jb21wb25lbnRzIGFzIElDb21wb25lbnRQcm9wc1tdfSBjbGFzc05hbWU9e3N0eWxlcy5ib2R5fT5cblx0XHRcdFx0PENvbHVtbkNvbXBvbmVudCAvPlxuXHRcdFx0XHQ8Q29sdW1uQWRkQmxvY2sgLz5cblx0XHRcdDwvQm9hcmQ+XG5cblx0XHRcdDxDb21wb25lbnRNZW51IHRyaWdnZXI9e21lbnV9IG9uQ2xvc2U9e2hhbmRsZUFkZEJsb2NrfSAvPlxuXHRcdDwvZGl2PlxuXHQpXG59XG4iXSwibmFtZXMiOlsiSWNvbiIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJ1c2VXaW5kb3dEaW1lbnNpb25zIiwiQ21zV3JhcHBlciIsImNuIiwiUmVhY3QiLCJ1c2VDYWxsYmFjayIsInVzZUNvbnRleHQiLCJ1c2VNZW1vIiwidXNlUmVmIiwidXNlU3RhdGUiLCJQYWdlQnVpbGRlckNvbnRleHQiLCJDb21wb25lbnRNZW51IiwiQ29tcG9uZW50UXVpY2tBY3Rpb25zIiwiQm9hcmQiLCJzdHlsZXMiLCJMYXlvdXRFZGl0b3IiLCJjaGlsZHJlbiIsImNvbnRleHQiLCJkYXRhIiwiY29udGVudFR5cGUiLCJjb25maWd1cmF0aW9uIiwic2V0RGF0YSIsImNvbXBvbmVudHMiLCJzZXRFZGl0aW5nSWRlbiIsIm5vcm1hbGl6ZWREYXRhIiwic2V0Q2hpbGRDb21wb25lbnREYXRhIiwiY29tbW9uRGF0YSIsInVpZENvbmZpZyIsInVpQ29uZmlnIiwid2luZG93RGltZW5zaW9uIiwiZ2xvYmFsRmllbGQiLCJzZXR0aW5ncyIsIm1haW5GaWVsZEtleSIsIm1haW5GaWVsZCIsImhlYWRsaW5lIiwic2V0SGVhZGxpbmUiLCJ0ZXh0YXJlYVJlZiIsImhhbmRsZUNoYW5nZSIsImV2ZW50IiwiY3VycmVudCIsInRhcmdldCIsInZhbHVlIiwic3R5bGUiLCJoZWlnaHQiLCJzY3JvbGxIZWlnaHQiLCJtZW51Iiwic2V0TWVudSIsInRyaWdnZXJNZW51IiwiZSIsImNvbnRhaW5lciIsImN1cnJlbnRUYXJnZXQiLCJwcmV2IiwiaGFuZGxlQWRkQmxvY2siLCJjb21wb25lbnQiLCJpZCIsIk51bWJlciIsImRhdGFzZXQiLCJkZWZhdWx0RGF0YSIsImZpbmQiLCJpdGVtIiwidWlkIiwiYXR0cmlidXRlcyIsInNjaGVtYSIsInJlbWFwUHJvcHMiLCJPYmplY3QiLCJlbnRyaWVzIiwicmVkdWNlIiwiYWNjIiwia2V5IiwibmV3VmFsdWUiLCJ0eXBlIiwiZGVmYXVsdCIsImFkZERhdGEiLCJfX2NvbXBvbmVudCIsIl9fdGVtcF9rZXlfXyIsImxlbmd0aCIsImluZGV4IiwiZmluZEluZGV4Iiwic3BsaWNlIiwiTW9kdWxlcyIsImFyckNvbXBvbmVudHMiLCJmb3JFYWNoIiwibW9kdWxlIiwiQ29tcG9uZW50IiwiQ29tcG9uZW50V3JhcHBlciIsImNvbHVtbiIsIk1vZHVsZSIsImlzSG92ZXJlZCIsInNldElzSG92ZXJlZCIsImhhbmRsZUVkaXQiLCJjb25zb2xlIiwibG9nIiwiZGl2IiwidGFiSW5kZXgiLCJyb2xlIiwiY2xhc3NOYW1lIiwiY29tcG9uZW50X19ibG9jayIsIm9uQ2xpY2siLCJvbktleURvd24iLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJpc1Zpc2libGUiLCJjb21wb25lbnRfX2Vycm9yIiwiQ29sdW1uQ29tcG9uZW50IiwicHJvcHMiLCJDb2x1bW5BZGRCbG9jayIsImJ1dHRvbiIsImRhdGEtaWQiLCJhZGRfX2Jsb2NrIiwidmFyaWFudCIsInNwYW4iLCJsaW5lIiwiYWxsQmxvY2tCdG4iLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJjbGFzc0xpc3QiLCJ0b2dnbGUiLCJ3cmFwcGVyIiwiaGVhZGVyIiwiYWRkX19pbWFnZSIsInRleHRhcmVhIiwicm93cyIsInBsYWNlaG9sZGVyIiwicmVmIiwib25DaGFuZ2UiLCJpbml0aWFsIiwiYm9keSIsInRyaWdnZXIiLCJvbkNsb3NlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx\n"));

/***/ })

});