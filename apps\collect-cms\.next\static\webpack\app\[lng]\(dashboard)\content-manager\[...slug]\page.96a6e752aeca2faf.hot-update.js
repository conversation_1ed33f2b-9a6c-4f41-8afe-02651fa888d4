/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss":
/*!**********************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/layouteditor.module.scss ***!
  \**********************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"wrapper\":\"layouteditor_wrapper__DTQyS\",\"headline\":\"layouteditor_headline__O4_34\",\"header\":\"layouteditor_header__z3D_M\",\"add__image\":\"layouteditor_add__image__xBFIf\",\"add__block\":\"layouteditor_add__block__1QfXW\",\"body\":\"layouteditor_body__W9sTh\",\"line\":\"layouteditor_line__OYy4W\",\"component__block__error\":\"layouteditor_component__block__error__lrOqP\"};\n    if(true) {\n      // 1748423880013\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"56e5b16bd8de\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0J1aWxkZXIvTGF5b3V0RWRpdG9yL2xheW91dGVkaXRvci5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQjtBQUNsQixPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBbUosY0FBYyxzREFBc0Q7QUFDclAsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9CdWlsZGVyL0xheW91dEVkaXRvci9sYXlvdXRlZGl0b3IubW9kdWxlLnNjc3M/NzdmMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wid3JhcHBlclwiOlwibGF5b3V0ZWRpdG9yX3dyYXBwZXJfX0RUUXlTXCIsXCJoZWFkbGluZVwiOlwibGF5b3V0ZWRpdG9yX2hlYWRsaW5lX19PNF8zNFwiLFwiaGVhZGVyXCI6XCJsYXlvdXRlZGl0b3JfaGVhZGVyX196M0RfTVwiLFwiYWRkX19pbWFnZVwiOlwibGF5b3V0ZWRpdG9yX2FkZF9faW1hZ2VfX3hCRklmXCIsXCJhZGRfX2Jsb2NrXCI6XCJsYXlvdXRlZGl0b3JfYWRkX19ibG9ja19fMVFmWFdcIixcImJvZHlcIjpcImxheW91dGVkaXRvcl9ib2R5X19XOXNUaFwiLFwibGluZVwiOlwibGF5b3V0ZWRpdG9yX2xpbmVfX09ZeTRXXCIsXCJjb21wb25lbnRfX2Jsb2NrX19lcnJvclwiOlwibGF5b3V0ZWRpdG9yX2NvbXBvbmVudF9fYmxvY2tfX2Vycm9yX19sck9xUFwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzQ4NDIzODgwMDEzXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkQ6L0NEQS9yZXBvcy9icmFuZC1jb21wYXNzLWZyb250ZW5kLXRlbXBsYXRlL2FwcHMvY29sbGVjdC1jbXMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gIFxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiNTZlNWIxNmJkOGRlXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\n"));

/***/ })

});